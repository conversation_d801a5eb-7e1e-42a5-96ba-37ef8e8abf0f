{"indexes": [{"collectionGroup": "tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "fechaCreacion", "order": "DESCENDING"}]}, {"collectionGroup": "tickets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "estado", "order": "ASCENDING"}, {"fieldPath": "fechaCreacion", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "fechaCreacion", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "leida", "order": "ASCENDING"}, {"fieldPath": "fechaCreacion", "order": "DESCENDING"}]}, {"collectionGroup": "bank_cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "propietarioEmail", "order": "ASCENDING"}, {"fieldPath": "fechaCreacion", "order": "DESCENDING"}]}, {"collectionGroup": "bank_cards", "queryScope": "COLLECTION", "fields": [{"fieldPath": "propietarioUID", "order": "ASCENDING"}, {"fieldPath": "activa", "order": "ASCENDING"}, {"fieldPath": "fechaCreacion", "order": "DESCENDING"}]}, {"collectionGroup": "user_metrics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "ultimaVisita", "order": "DESCENDING"}]}, {"collectionGroup": "detailed_visits", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "detailed_visits", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userRegion", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "bank_usage", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "bank_usage", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userRegion", "order": "ASCENDING"}, {"fieldPath": "bankName", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vendedor", "order": "ASCENDING"}, {"fieldPath": "rol", "order": "ASCENDING"}, {"fieldPath": "fechaRegistro", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "vendedor", "order": "ASCENDING"}, {"fieldPath": "rol", "order": "ASCENDING"}]}], "fieldOverrides": []}