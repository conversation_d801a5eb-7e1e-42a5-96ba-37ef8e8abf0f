// Script para crear un usuario de prueba
const { initializeApp } = require("firebase/app");
const { getAuth, createUserWithEmailAndPassword } = require("firebase/auth");
const { getFirestore, doc, setDoc } = require("firebase/firestore");

const firebaseConfig = {
  apiKey: "AIzaSyAFfjqq8YIfrduQpiYoMLGreId4vhkX08M",
  authDomain: "deunamaikostudios.firebaseapp.com",
  projectId: "deunamaikostudios",
  storageBucket: "deunamaikostudios.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:009b0b4885464ece0bc9c6",
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

async function createTestUser() {
  try {
    // Crear usuario de prueba
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      "<EMAIL>",
      "123456"
    );

    console.log("Usuario creado:", userCredential.user.uid);

    // Crear documento en Firestore con rol
    await setDoc(doc(db, "users", userCredential.user.uid), {
      email: "<EMAIL>",
      role: "user",
      nombre: "Usuario de Prueba",
      fechaRegistro: new Date(),
    });

    console.log("Documento de usuario creado en Firestore");

    // Crear usuario admin
    const adminCredential = await createUserWithEmailAndPassword(
      auth,
      "<EMAIL>",
      "123456"
    );

    console.log("Usuario admin creado:", adminCredential.user.uid);

    await setDoc(doc(db, "users", adminCredential.user.uid), {
      email: "<EMAIL>",
      role: "admin",
      nombre: "Administrador",
      fechaRegistro: new Date(),
    });

    console.log("Documento de admin creado en Firestore");

    // Crear usuario vendedor
    const vendedorCredential = await createUserWithEmailAndPassword(
      auth,
      "<EMAIL>",
      "123456"
    );

    console.log("Usuario vendedor creado:", vendedorCredential.user.uid);

    await setDoc(doc(db, "users", vendedorCredential.user.uid), {
      email: "<EMAIL>",
      role: "vendedor",
      nombre: "Vendedor",
      fechaRegistro: new Date(),
    });

    console.log("Documento de vendedor creado en Firestore");

    console.log("\n✅ Usuarios de prueba creados exitosamente:");
    console.log("👤 Usuario normal: <EMAIL> / 123456");
    console.log("👑 Admin: <EMAIL> / 123456");
    console.log("💼 Vendedor: <EMAIL> / 123456");
  } catch (error) {
    console.error("Error creando usuarios:", error);
  }
}

createTestUser();
