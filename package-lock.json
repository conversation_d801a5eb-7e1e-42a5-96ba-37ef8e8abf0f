{"name": "altoque-vue-mvp", "version": "0.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "altoque-vue-mvp", "version": "0.0.0", "dependencies": {"@mdi/font": "^7.4.47", "altoque-vue-mvp": "file:", "bootstrap-icons": "^1.11.3", "dotenv": "^16.5.0", "firebase": "^11.6.0", "pinia": "^3.0.1", "qrcode.vue": "^3.6.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.1", "webfontloader": "^1.6.28"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.3", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "sass": "^1.86.3", "sass-loader": "^13.3.3", "tailwindcss": "^4.1.3", "vite": "^6.2.0", "vite-plugin-vuetify": "^2.1.1"}}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/helper-string-parser": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.0", "license": "MIT", "dependencies": {"@babel/types": "^7.27.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/types": {"version": "7.27.0", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.2", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@firebase/analytics": {"version": "0.10.12", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/analytics-compat": {"version": "0.2.18", "license": "Apache-2.0", "dependencies": {"@firebase/analytics": "0.10.12", "@firebase/analytics-types": "0.8.3", "@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/analytics-types": {"version": "0.8.3", "license": "Apache-2.0"}, "node_modules/@firebase/app": {"version": "0.11.4", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/app-check": {"version": "0.8.13", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/app-check-compat": {"version": "0.3.20", "license": "Apache-2.0", "dependencies": {"@firebase/app-check": "0.8.13", "@firebase/app-check-types": "0.5.3", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/app-check-interop-types": {"version": "0.3.3", "license": "Apache-2.0"}, "node_modules/@firebase/app-check-types": {"version": "0.5.3", "license": "Apache-2.0"}, "node_modules/@firebase/app-compat": {"version": "0.2.53", "license": "Apache-2.0", "dependencies": {"@firebase/app": "0.11.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/app-types": {"version": "0.9.3", "license": "Apache-2.0"}, "node_modules/@firebase/auth": {"version": "1.10.0", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x", "@react-native-async-storage/async-storage": "^1.18.1"}, "peerDependenciesMeta": {"@react-native-async-storage/async-storage": {"optional": true}}}, "node_modules/@firebase/auth-compat": {"version": "0.5.20", "license": "Apache-2.0", "dependencies": {"@firebase/auth": "1.10.0", "@firebase/auth-types": "0.13.0", "@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/auth-interop-types": {"version": "0.2.4", "license": "Apache-2.0"}, "node_modules/@firebase/auth-types": {"version": "0.13.0", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x", "@firebase/util": "1.x"}}, "node_modules/@firebase/component": {"version": "0.6.13", "license": "Apache-2.0", "dependencies": {"@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/data-connect": {"version": "0.3.3", "license": "Apache-2.0", "dependencies": {"@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/database": {"version": "1.0.14", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "faye-websocket": "0.11.4", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/database-compat": {"version": "2.0.5", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/database": "1.0.14", "@firebase/database-types": "1.0.10", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/database-types": {"version": "1.0.10", "license": "Apache-2.0", "dependencies": {"@firebase/app-types": "0.9.3", "@firebase/util": "1.11.0"}}, "node_modules/@firebase/firestore": {"version": "4.7.10", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "@firebase/webchannel-wrapper": "1.0.3", "@grpc/grpc-js": "~1.9.0", "@grpc/proto-loader": "^0.7.8", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/firestore-compat": {"version": "0.3.45", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/firestore": "4.7.10", "@firebase/firestore-types": "3.0.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/firestore-types": {"version": "3.0.3", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x", "@firebase/util": "1.x"}}, "node_modules/@firebase/functions": {"version": "0.12.3", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/functions-compat": {"version": "0.3.20", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/functions": "0.12.3", "@firebase/functions-types": "0.6.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/functions-types": {"version": "0.6.3", "license": "Apache-2.0"}, "node_modules/@firebase/installations": {"version": "0.6.13", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/installations-compat": {"version": "0.2.13", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/installations-types": "0.5.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/installations-types": {"version": "0.5.3", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x"}}, "node_modules/@firebase/logger": {"version": "0.4.4", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/messaging": {"version": "0.12.17", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/messaging-compat": {"version": "0.2.17", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/messaging": "0.12.17", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/messaging-interop-types": {"version": "0.2.3", "license": "Apache-2.0"}, "node_modules/@firebase/performance": {"version": "0.7.2", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0", "web-vitals": "^4.2.4"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/performance-compat": {"version": "0.2.15", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/performance": "0.7.2", "@firebase/performance-types": "0.2.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/performance-types": {"version": "0.2.3", "license": "Apache-2.0"}, "node_modules/@firebase/remote-config": {"version": "0.6.0", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/remote-config-compat": {"version": "0.2.13", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/remote-config": "0.6.0", "@firebase/remote-config-types": "0.4.0", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/remote-config-types": {"version": "0.4.0", "license": "Apache-2.0"}, "node_modules/@firebase/storage": {"version": "0.13.7", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x"}}, "node_modules/@firebase/storage-compat": {"version": "0.3.17", "license": "Apache-2.0", "dependencies": {"@firebase/component": "0.6.13", "@firebase/storage": "0.13.7", "@firebase/storage-types": "0.8.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app-compat": "0.x"}}, "node_modules/@firebase/storage-types": {"version": "0.8.3", "license": "Apache-2.0", "peerDependencies": {"@firebase/app-types": "0.x", "@firebase/util": "1.x"}}, "node_modules/@firebase/util": {"version": "1.11.0", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@firebase/vertexai": {"version": "1.2.1", "license": "Apache-2.0", "dependencies": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"@firebase/app": "0.x", "@firebase/app-types": "0.x"}}, "node_modules/@firebase/webchannel-wrapper": {"version": "1.0.3", "license": "Apache-2.0"}, "node_modules/@grpc/grpc-js": {"version": "1.9.15", "license": "Apache-2.0", "dependencies": {"@grpc/proto-loader": "^0.7.8", "@types/node": ">=12.12.47"}, "engines": {"node": "^8.13.0 || >=10.10.0"}}, "node_modules/@grpc/proto-loader": {"version": "0.7.13", "license": "Apache-2.0", "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "bin": {"proto-loader-gen-types": "build/bin/proto-loader-gen-types.js"}, "engines": {"node": ">=6"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "devOptional": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "devOptional": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "devOptional": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "devOptional": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "devOptional": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@mdi/font": {"version": "7.4.47", "license": "Apache-2.0"}, "node_modules/@parcel/watcher": {"version": "2.5.1", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "node_modules/@parcel/watcher-win32-x64": {"version": "2.5.1", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher/node_modules/detect-libc": {"version": "1.0.3", "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/path": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.39.0", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@tailwindcss/node": {"version": "4.1.3", "dev": true, "license": "MIT", "dependencies": {"enhanced-resolve": "^5.18.1", "jiti": "^2.4.2", "lightningcss": "1.29.2", "tailwindcss": "4.1.3"}}, "node_modules/@tailwindcss/oxide": {"version": "4.1.3", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}, "optionalDependencies": {"@tailwindcss/oxide-android-arm64": "4.1.3", "@tailwindcss/oxide-darwin-arm64": "4.1.3", "@tailwindcss/oxide-darwin-x64": "4.1.3", "@tailwindcss/oxide-freebsd-x64": "4.1.3", "@tailwindcss/oxide-linux-arm-gnueabihf": "4.1.3", "@tailwindcss/oxide-linux-arm64-gnu": "4.1.3", "@tailwindcss/oxide-linux-arm64-musl": "4.1.3", "@tailwindcss/oxide-linux-x64-gnu": "4.1.3", "@tailwindcss/oxide-linux-x64-musl": "4.1.3", "@tailwindcss/oxide-win32-arm64-msvc": "4.1.3", "@tailwindcss/oxide-win32-x64-msvc": "4.1.3"}}, "node_modules/@tailwindcss/oxide-win32-x64-msvc": {"version": "4.1.3", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/postcss": {"version": "4.1.3", "dev": true, "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "@tailwindcss/node": "4.1.3", "@tailwindcss/oxide": "4.1.3", "postcss": "^8.4.41", "tailwindcss": "4.1.3"}}, "node_modules/@types/eslint": {"version": "9.6.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.7", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/estree": {"version": "1.0.7", "devOptional": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT", "peer": true}, "node_modules/@types/node": {"version": "22.14.0", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@vitejs/plugin-vue": {"version": "5.2.3", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}}, "node_modules/@vue/compiler-core": {"version": "3.5.13", "license": "MIT", "dependencies": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.13", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.13", "@vue/shared": "3.5.13"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.13", "license": "MIT", "dependencies": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.13", "@vue/compiler-dom": "3.5.13", "@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.48", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.13", "@vue/shared": "3.5.13"}}, "node_modules/@vue/devtools-api": {"version": "7.7.2", "license": "MIT", "dependencies": {"@vue/devtools-kit": "^7.7.2"}}, "node_modules/@vue/devtools-kit": {"version": "7.7.2", "license": "MIT", "dependencies": {"@vue/devtools-shared": "^7.7.2", "birpc": "^0.2.19", "hookable": "^5.5.3", "mitt": "^3.0.1", "perfect-debounce": "^1.0.0", "speakingurl": "^14.0.1", "superjson": "^2.2.1"}}, "node_modules/@vue/devtools-shared": {"version": "7.7.2", "license": "MIT", "dependencies": {"rfdc": "^1.4.1"}}, "node_modules/@vue/reactivity": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/shared": "3.5.13"}}, "node_modules/@vue/runtime-core": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.13", "@vue/shared": "3.5.13"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.13", "@vue/runtime-core": "3.5.13", "@vue/shared": "3.5.13", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13"}, "peerDependencies": {"vue": "3.5.13"}}, "node_modules/@vue/shared": {"version": "3.5.13", "license": "MIT"}, "node_modules/@vuetify/loader-shared": {"version": "2.1.0", "devOptional": true, "license": "MIT", "dependencies": {"upath": "^2.0.1"}, "peerDependencies": {"vue": "^3.0.0", "vuetify": "^3.0.0"}}, "node_modules/@webassemblyjs/ast": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "dev": true, "license": "Apache-2.0", "peer": true, "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@xtuc/long": {"version": "4.2.2", "dev": true, "license": "Apache-2.0", "peer": true}, "node_modules/acorn": {"version": "8.14.1", "devOptional": true, "license": "MIT", "peer": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/altoque-vue-mvp": {"resolved": "", "link": true}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/autoprefixer": {"version": "10.4.21", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/birpc": {"version": "0.2.19", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/bootstrap-icons": {"version": "1.11.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "license": "MIT"}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "optional": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.24.4", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer-from": {"version": "1.1.2", "devOptional": true, "license": "MIT", "peer": true}, "node_modules/caniuse-lite": {"version": "1.0.30001712", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chokidar": {"version": "4.0.3", "devOptional": true, "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.0"}}, "node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/commander": {"version": "2.20.3", "devOptional": true, "license": "MIT", "peer": true}, "node_modules/copy-anything": {"version": "3.0.5", "license": "MIT", "dependencies": {"is-what": "^4.1.8"}, "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/debug": {"version": "4.4.0", "devOptional": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/detect-libc": {"version": "2.0.3", "devOptional": true, "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/dotenv": {"version": "16.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/electron-to-chromium": {"version": "1.5.134", "dev": true, "license": "ISC"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/enhanced-resolve": {"version": "5.18.1", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/es-module-lexer": {"version": "1.6.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/esbuild": {"version": "0.25.2", "devOptional": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.2", "@esbuild/android-arm": "0.25.2", "@esbuild/android-arm64": "0.25.2", "@esbuild/android-x64": "0.25.2", "@esbuild/darwin-arm64": "0.25.2", "@esbuild/darwin-x64": "0.25.2", "@esbuild/freebsd-arm64": "0.25.2", "@esbuild/freebsd-x64": "0.25.2", "@esbuild/linux-arm": "0.25.2", "@esbuild/linux-arm64": "0.25.2", "@esbuild/linux-ia32": "0.25.2", "@esbuild/linux-loong64": "0.25.2", "@esbuild/linux-mips64el": "0.25.2", "@esbuild/linux-ppc64": "0.25.2", "@esbuild/linux-riscv64": "0.25.2", "@esbuild/linux-s390x": "0.25.2", "@esbuild/linux-x64": "0.25.2", "@esbuild/netbsd-arm64": "0.25.2", "@esbuild/netbsd-x64": "0.25.2", "@esbuild/openbsd-arm64": "0.25.2", "@esbuild/openbsd-x64": "0.25.2", "@esbuild/sunos-x64": "0.25.2", "@esbuild/win32-arm64": "0.25.2", "@esbuild/win32-ia32": "0.25.2", "@esbuild/win32-x64": "0.25.2"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.8.x"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT", "peer": true}, "node_modules/fast-uri": {"version": "3.0.6", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/faye-websocket": {"version": "0.11.4", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "optional": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/firebase": {"version": "11.6.0", "license": "Apache-2.0", "dependencies": {"@firebase/analytics": "0.10.12", "@firebase/analytics-compat": "0.2.18", "@firebase/app": "0.11.4", "@firebase/app-check": "0.8.13", "@firebase/app-check-compat": "0.3.20", "@firebase/app-compat": "0.2.53", "@firebase/app-types": "0.9.3", "@firebase/auth": "1.10.0", "@firebase/auth-compat": "0.5.20", "@firebase/data-connect": "0.3.3", "@firebase/database": "1.0.14", "@firebase/database-compat": "2.0.5", "@firebase/firestore": "4.7.10", "@firebase/firestore-compat": "0.3.45", "@firebase/functions": "0.12.3", "@firebase/functions-compat": "0.3.20", "@firebase/installations": "0.6.13", "@firebase/installations-compat": "0.2.13", "@firebase/messaging": "0.12.17", "@firebase/messaging-compat": "0.2.17", "@firebase/performance": "0.7.2", "@firebase/performance-compat": "0.2.15", "@firebase/remote-config": "0.6.0", "@firebase/remote-config-compat": "0.2.13", "@firebase/storage": "0.13.7", "@firebase/storage-compat": "0.3.17", "@firebase/util": "1.11.0", "@firebase/vertexai": "1.2.1"}}, "node_modules/fraction.js": {"version": "4.3.7", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true}, "node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC"}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=8"}}, "node_modules/hookable": {"version": "5.5.3", "license": "MIT"}, "node_modules/http-parser-js": {"version": "0.5.10", "license": "MIT"}, "node_modules/idb": {"version": "7.1.1", "license": "ISC"}, "node_modules/immutable": {"version": "5.1.1", "devOptional": true, "license": "MIT"}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "optional": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.12.0"}}, "node_modules/is-what": {"version": "4.1.16", "license": "MIT", "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/jest-worker": {"version": "27.5.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jiti": {"version": "2.4.2", "devOptional": true, "license": "MIT", "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT", "peer": true}, "node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lightningcss": {"version": "1.29.2", "devOptional": true, "license": "MPL-2.0", "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.29.2", "lightningcss-darwin-x64": "1.29.2", "lightningcss-freebsd-x64": "1.29.2", "lightningcss-linux-arm-gnueabihf": "1.29.2", "lightningcss-linux-arm64-gnu": "1.29.2", "lightningcss-linux-arm64-musl": "1.29.2", "lightningcss-linux-x64-gnu": "1.29.2", "lightningcss-linux-x64-musl": "1.29.2", "lightningcss-win32-arm64-msvc": "1.29.2", "lightningcss-win32-x64-msvc": "1.29.2"}}, "node_modules/lightningcss-win32-x64-msvc": {"version": "1.29.2", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/loader-runner": {"version": "4.3.0", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.11.5"}}, "node_modules/lodash.camelcase": {"version": "4.3.0", "license": "MIT"}, "node_modules/long": {"version": "5.3.1", "license": "Apache-2.0"}, "node_modules/magic-string": {"version": "0.30.17", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/merge-stream": {"version": "2.0.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "optional": true, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "dev": true, "license": "MIT", "peer": true, "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mitt": {"version": "3.0.1", "license": "MIT"}, "node_modules/ms": {"version": "2.1.3", "devOptional": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/neo-async": {"version": "2.6.2", "dev": true, "license": "MIT"}, "node_modules/node-addon-api": {"version": "7.1.1", "license": "MIT", "optional": true}, "node_modules/node-releases": {"version": "2.0.19", "dev": true, "license": "MIT"}, "node_modules/normalize-range": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/perfect-debounce": {"version": "1.0.0", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "optional": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pinia": {"version": "3.0.1", "license": "MIT", "dependencies": {"@vue/devtools-api": "^7.7.2"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"typescript": ">=4.4.4", "vue": "^2.7.0 || ^3.5.11"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/postcss": {"version": "8.5.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "dev": true, "license": "MIT"}, "node_modules/protobufjs": {"version": "7.4.0", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/qrcode.vue": {"version": "3.6.0", "license": "MIT", "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/randombytes": {"version": "2.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/readdirp": {"version": "4.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "license": "MIT"}, "node_modules/rollup": {"version": "4.39.0", "devOptional": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.39.0", "@rollup/rollup-android-arm64": "4.39.0", "@rollup/rollup-darwin-arm64": "4.39.0", "@rollup/rollup-darwin-x64": "4.39.0", "@rollup/rollup-freebsd-arm64": "4.39.0", "@rollup/rollup-freebsd-x64": "4.39.0", "@rollup/rollup-linux-arm-gnueabihf": "4.39.0", "@rollup/rollup-linux-arm-musleabihf": "4.39.0", "@rollup/rollup-linux-arm64-gnu": "4.39.0", "@rollup/rollup-linux-arm64-musl": "4.39.0", "@rollup/rollup-linux-loongarch64-gnu": "4.39.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-musl": "4.39.0", "@rollup/rollup-linux-s390x-gnu": "4.39.0", "@rollup/rollup-linux-x64-gnu": "4.39.0", "@rollup/rollup-linux-x64-musl": "4.39.0", "@rollup/rollup-win32-arm64-msvc": "4.39.0", "@rollup/rollup-win32-ia32-msvc": "4.39.0", "@rollup/rollup-win32-x64-msvc": "4.39.0", "fsevents": "~2.3.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/sass": {"version": "1.86.3", "devOptional": true, "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "node_modules/sass-loader": {"version": "13.3.3", "dev": true, "license": "MIT", "dependencies": {"neo-async": "^2.6.2"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"fibers": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}}}, "node_modules/schema-utils": {"version": "4.3.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/source-map": {"version": "0.6.1", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "devOptional": true, "license": "MIT", "peer": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/speakingurl": {"version": "14.0.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/superjson": {"version": "2.2.2", "license": "MIT", "dependencies": {"copy-anything": "^3.0.2"}, "engines": {"node": ">=16"}}, "node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/tailwindcss": {"version": "4.1.3", "dev": true, "license": "MIT"}, "node_modules/tapable": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "5.39.0", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.14", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "optional": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/undici-types": {"version": "6.21.0", "license": "MIT"}, "node_modules/upath": {"version": "2.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uuid": {"version": "11.1.0", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}}, "node_modules/vite": {"version": "6.2.5", "devOptional": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "postcss": "^8.5.3", "rollup": "^4.30.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-plugin-vuetify": {"version": "2.1.1", "devOptional": true, "license": "MIT", "dependencies": {"@vuetify/loader-shared": "^2.1.0", "debug": "^4.3.3", "upath": "^2.0.1"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": ">=5", "vue": "^3.0.0", "vuetify": "^3.0.0"}}, "node_modules/vue": {"version": "3.5.13", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.13", "@vue/compiler-sfc": "3.5.13", "@vue/runtime-dom": "3.5.13", "@vue/server-renderer": "3.5.13", "@vue/shared": "3.5.13"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-router": {"version": "4.5.0", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vue-router/node_modules/@vue/devtools-api": {"version": "6.6.4", "license": "MIT"}, "node_modules/vuetify": {"version": "3.8.1", "license": "MIT", "engines": {"node": "^12.20 || >=14.13"}, "funding": {"type": "github", "url": "https://github.com/sponsors/johnleider"}, "peerDependencies": {"typescript": ">=4.7", "vite-plugin-vuetify": ">=2.1.0", "vue": "^3.5.0", "webpack-plugin-vuetify": ">=3.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "vite-plugin-vuetify": {"optional": true}, "webpack-plugin-vuetify": {"optional": true}}}, "node_modules/watchpack": {"version": "2.4.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/web-vitals": {"version": "4.2.4", "license": "Apache-2.0"}, "node_modules/webfontloader": {"version": "1.6.28", "license": "Apache-2.0"}, "node_modules/webpack": {"version": "5.99.3", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-sources": {"version": "3.2.3", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=10.13.0"}}, "node_modules/websocket-driver": {"version": "0.7.4", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}}, "dependencies": {"@alloc/quick-lru": {"version": "5.2.0", "dev": true}, "@babel/helper-string-parser": {"version": "7.25.9"}, "@babel/helper-validator-identifier": {"version": "7.25.9"}, "@babel/parser": {"version": "7.27.0", "requires": {"@babel/types": "^7.27.0"}}, "@babel/types": {"version": "7.27.0", "requires": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}}, "@esbuild/win32-x64": {"version": "0.25.2", "optional": true}, "@firebase/analytics": {"version": "0.10.12", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/analytics-compat": {"version": "0.2.18", "requires": {"@firebase/analytics": "0.10.12", "@firebase/analytics-types": "0.8.3", "@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/analytics-types": {"version": "0.8.3"}, "@firebase/app": {"version": "0.11.4", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}}, "@firebase/app-check": {"version": "0.8.13", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/app-check-compat": {"version": "0.3.20", "requires": {"@firebase/app-check": "0.8.13", "@firebase/app-check-types": "0.5.3", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/app-check-interop-types": {"version": "0.3.3"}, "@firebase/app-check-types": {"version": "0.5.3"}, "@firebase/app-compat": {"version": "0.2.53", "requires": {"@firebase/app": "0.11.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/app-types": {"version": "0.9.3"}, "@firebase/auth": {"version": "1.10.0", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/auth-compat": {"version": "0.5.20", "requires": {"@firebase/auth": "1.10.0", "@firebase/auth-types": "0.13.0", "@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/auth-interop-types": {"version": "0.2.4"}, "@firebase/auth-types": {"version": "0.13.0", "requires": {}}, "@firebase/component": {"version": "0.6.13", "requires": {"@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/data-connect": {"version": "0.3.3", "requires": {"@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/database": {"version": "1.0.14", "requires": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "faye-websocket": "0.11.4", "tslib": "^2.1.0"}}, "@firebase/database-compat": {"version": "2.0.5", "requires": {"@firebase/component": "0.6.13", "@firebase/database": "1.0.14", "@firebase/database-types": "1.0.10", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/database-types": {"version": "1.0.10", "requires": {"@firebase/app-types": "0.9.3", "@firebase/util": "1.11.0"}}, "@firebase/firestore": {"version": "4.7.10", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "@firebase/webchannel-wrapper": "1.0.3", "@grpc/grpc-js": "~1.9.0", "@grpc/proto-loader": "^0.7.8", "tslib": "^2.1.0"}}, "@firebase/firestore-compat": {"version": "0.3.45", "requires": {"@firebase/component": "0.6.13", "@firebase/firestore": "4.7.10", "@firebase/firestore-types": "3.0.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/firestore-types": {"version": "3.0.3", "requires": {}}, "@firebase/functions": {"version": "0.12.3", "requires": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/functions-compat": {"version": "0.3.20", "requires": {"@firebase/component": "0.6.13", "@firebase/functions": "0.12.3", "@firebase/functions-types": "0.6.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/functions-types": {"version": "0.6.3"}, "@firebase/installations": {"version": "0.6.13", "requires": {"@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}}, "@firebase/installations-compat": {"version": "0.2.13", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/installations-types": "0.5.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/installations-types": {"version": "0.5.3", "requires": {}}, "@firebase/logger": {"version": "0.4.4", "requires": {"tslib": "^2.1.0"}}, "@firebase/messaging": {"version": "0.12.17", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}}, "@firebase/messaging-compat": {"version": "0.2.17", "requires": {"@firebase/component": "0.6.13", "@firebase/messaging": "0.12.17", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/messaging-interop-types": {"version": "0.2.3"}, "@firebase/performance": {"version": "0.7.2", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0", "web-vitals": "^4.2.4"}}, "@firebase/performance-compat": {"version": "0.2.15", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/performance": "0.7.2", "@firebase/performance-types": "0.2.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/performance-types": {"version": "0.2.3"}, "@firebase/remote-config": {"version": "0.6.0", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/remote-config-compat": {"version": "0.2.13", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/remote-config": "0.6.0", "@firebase/remote-config-types": "0.4.0", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/remote-config-types": {"version": "0.4.0"}, "@firebase/storage": {"version": "0.13.7", "requires": {"@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/storage-compat": {"version": "0.3.17", "requires": {"@firebase/component": "0.6.13", "@firebase/storage": "0.13.7", "@firebase/storage-types": "0.8.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/storage-types": {"version": "0.8.3", "requires": {}}, "@firebase/util": {"version": "1.11.0", "requires": {"tslib": "^2.1.0"}}, "@firebase/vertexai": {"version": "1.2.1", "requires": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/webchannel-wrapper": {"version": "1.0.3"}, "@grpc/grpc-js": {"version": "1.9.15", "requires": {"@grpc/proto-loader": "^0.7.8", "@types/node": ">=12.12.47"}}, "@grpc/proto-loader": {"version": "0.7.13", "requires": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}}, "@jridgewell/gen-mapping": {"version": "0.3.8", "devOptional": true, "peer": true, "requires": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}}, "@jridgewell/resolve-uri": {"version": "3.1.2", "devOptional": true, "peer": true}, "@jridgewell/set-array": {"version": "1.2.1", "devOptional": true, "peer": true}, "@jridgewell/source-map": {"version": "0.3.6", "devOptional": true, "peer": true, "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "@jridgewell/sourcemap-codec": {"version": "1.5.0"}, "@jridgewell/trace-mapping": {"version": "0.3.25", "devOptional": true, "peer": true, "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@mdi/font": {"version": "7.4.47"}, "@parcel/watcher": {"version": "2.5.1", "optional": true, "requires": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1", "detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "dependencies": {"detect-libc": {"version": "1.0.3", "optional": true}}}, "@parcel/watcher-win32-x64": {"version": "2.5.1", "optional": true}, "@protobufjs/aspromise": {"version": "1.1.2"}, "@protobufjs/base64": {"version": "1.1.2"}, "@protobufjs/codegen": {"version": "2.0.4"}, "@protobufjs/eventemitter": {"version": "1.1.0"}, "@protobufjs/fetch": {"version": "1.1.0", "requires": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "@protobufjs/float": {"version": "1.0.2"}, "@protobufjs/inquire": {"version": "1.1.0"}, "@protobufjs/path": {"version": "1.1.2"}, "@protobufjs/pool": {"version": "1.1.0"}, "@protobufjs/utf8": {"version": "1.1.0"}, "@rollup/rollup-win32-x64-msvc": {"version": "4.39.0", "optional": true}, "@tailwindcss/node": {"version": "4.1.3", "dev": true, "requires": {"enhanced-resolve": "^5.18.1", "jiti": "^2.4.2", "lightningcss": "1.29.2", "tailwindcss": "4.1.3"}}, "@tailwindcss/oxide": {"version": "4.1.3", "dev": true, "requires": {"@tailwindcss/oxide-android-arm64": "4.1.3", "@tailwindcss/oxide-darwin-arm64": "4.1.3", "@tailwindcss/oxide-darwin-x64": "4.1.3", "@tailwindcss/oxide-freebsd-x64": "4.1.3", "@tailwindcss/oxide-linux-arm-gnueabihf": "4.1.3", "@tailwindcss/oxide-linux-arm64-gnu": "4.1.3", "@tailwindcss/oxide-linux-arm64-musl": "4.1.3", "@tailwindcss/oxide-linux-x64-gnu": "4.1.3", "@tailwindcss/oxide-linux-x64-musl": "4.1.3", "@tailwindcss/oxide-win32-arm64-msvc": "4.1.3", "@tailwindcss/oxide-win32-x64-msvc": "4.1.3"}}, "@tailwindcss/oxide-win32-x64-msvc": {"version": "4.1.3", "dev": true, "optional": true}, "@tailwindcss/postcss": {"version": "4.1.3", "dev": true, "requires": {"@alloc/quick-lru": "^5.2.0", "@tailwindcss/node": "4.1.3", "@tailwindcss/oxide": "4.1.3", "postcss": "^8.4.41", "tailwindcss": "4.1.3"}}, "@types/eslint": {"version": "9.6.1", "dev": true, "peer": true, "requires": {"@types/estree": "*", "@types/json-schema": "*"}}, "@types/eslint-scope": {"version": "3.7.7", "dev": true, "peer": true, "requires": {"@types/eslint": "*", "@types/estree": "*"}}, "@types/estree": {"version": "1.0.7", "devOptional": true}, "@types/json-schema": {"version": "7.0.15", "dev": true, "peer": true}, "@types/node": {"version": "22.14.0", "requires": {"undici-types": "~6.21.0"}}, "@vitejs/plugin-vue": {"version": "5.2.3", "dev": true, "requires": {}}, "@vue/compiler-core": {"version": "3.5.13", "requires": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.13", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "@vue/compiler-dom": {"version": "3.5.13", "requires": {"@vue/compiler-core": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/compiler-sfc": {"version": "3.5.13", "requires": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.13", "@vue/compiler-dom": "3.5.13", "@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.48", "source-map-js": "^1.2.0"}}, "@vue/compiler-ssr": {"version": "3.5.13", "requires": {"@vue/compiler-dom": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/devtools-api": {"version": "7.7.2", "requires": {"@vue/devtools-kit": "^7.7.2"}}, "@vue/devtools-kit": {"version": "7.7.2", "requires": {"@vue/devtools-shared": "^7.7.2", "birpc": "^0.2.19", "hookable": "^5.5.3", "mitt": "^3.0.1", "perfect-debounce": "^1.0.0", "speakingurl": "^14.0.1", "superjson": "^2.2.1"}}, "@vue/devtools-shared": {"version": "7.7.2", "requires": {"rfdc": "^1.4.1"}}, "@vue/reactivity": {"version": "3.5.13", "requires": {"@vue/shared": "3.5.13"}}, "@vue/runtime-core": {"version": "3.5.13", "requires": {"@vue/reactivity": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/runtime-dom": {"version": "3.5.13", "requires": {"@vue/reactivity": "3.5.13", "@vue/runtime-core": "3.5.13", "@vue/shared": "3.5.13", "csstype": "^3.1.3"}}, "@vue/server-renderer": {"version": "3.5.13", "requires": {"@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/shared": {"version": "3.5.13"}, "@vuetify/loader-shared": {"version": "2.1.0", "devOptional": true, "requires": {"upath": "^2.0.1"}}, "@webassemblyjs/ast": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/helper-api-error": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/helper-buffer": {"version": "1.14.1", "dev": true, "peer": true}, "@webassemblyjs/helper-numbers": {"version": "1.13.2", "dev": true, "peer": true, "requires": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "@webassemblyjs/ieee754": {"version": "1.13.2", "dev": true, "peer": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.13.2", "dev": true, "peer": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/wasm-edit": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "@webassemblyjs/wasm-gen": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "@webassemblyjs/wasm-opt": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "@webassemblyjs/wasm-parser": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "@webassemblyjs/wast-printer": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "dev": true, "peer": true}, "@xtuc/long": {"version": "4.2.2", "dev": true, "peer": true}, "acorn": {"version": "8.14.1", "devOptional": true, "peer": true}, "ajv": {"version": "8.17.1", "dev": true, "peer": true, "requires": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}}, "ajv-formats": {"version": "2.1.1", "dev": true, "peer": true, "requires": {"ajv": "^8.0.0"}}, "ajv-keywords": {"version": "5.1.0", "dev": true, "peer": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "altoque-vue-mvp": {"version": "file:", "requires": {"@mdi/font": "^7.4.47", "@tailwindcss/postcss": "^4.1.3", "@vitejs/plugin-vue": "^5.2.1", "altoque-vue-mvp": "file:", "autoprefixer": "^10.4.21", "bootstrap-icons": "^1.11.3", "dotenv": "^16.5.0", "firebase": "^11.6.0", "pinia": "^3.0.1", "postcss": "^8.5.3", "qrcode.vue": "^3.6.0", "sass": "^1.86.3", "sass-loader": "^13.3.3", "tailwindcss": "^4.1.3", "uuid": "^11.1.0", "vite": "^6.2.0", "vite-plugin-vuetify": "^2.1.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.1", "webfontloader": "^1.6.28"}, "dependencies": {"@alloc/quick-lru": {"version": "5.2.0", "dev": true}, "@babel/helper-string-parser": {"version": "7.25.9"}, "@babel/helper-validator-identifier": {"version": "7.25.9"}, "@babel/parser": {"version": "7.27.0", "requires": {"@babel/types": "^7.27.0"}}, "@babel/types": {"version": "7.27.0", "requires": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}}, "@esbuild/win32-x64": {"version": "0.25.2", "optional": true}, "@firebase/analytics": {"version": "0.10.12", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/analytics-compat": {"version": "0.2.18", "requires": {"@firebase/analytics": "0.10.12", "@firebase/analytics-types": "0.8.3", "@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/analytics-types": {"version": "0.8.3"}, "@firebase/app": {"version": "0.11.4", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}}, "@firebase/app-check": {"version": "0.8.13", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/app-check-compat": {"version": "0.3.20", "requires": {"@firebase/app-check": "0.8.13", "@firebase/app-check-types": "0.5.3", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/app-check-interop-types": {"version": "0.3.3"}, "@firebase/app-check-types": {"version": "0.5.3"}, "@firebase/app-compat": {"version": "0.2.53", "requires": {"@firebase/app": "0.11.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/app-types": {"version": "0.9.3"}, "@firebase/auth": {"version": "1.10.0", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/auth-compat": {"version": "0.5.20", "requires": {"@firebase/auth": "1.10.0", "@firebase/auth-types": "0.13.0", "@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/auth-interop-types": {"version": "0.2.4"}, "@firebase/auth-types": {"version": "0.13.0", "requires": {}}, "@firebase/component": {"version": "0.6.13", "requires": {"@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/data-connect": {"version": "0.3.3", "requires": {"@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/database": {"version": "1.0.14", "requires": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "faye-websocket": "0.11.4", "tslib": "^2.1.0"}}, "@firebase/database-compat": {"version": "2.0.5", "requires": {"@firebase/component": "0.6.13", "@firebase/database": "1.0.14", "@firebase/database-types": "1.0.10", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/database-types": {"version": "1.0.10", "requires": {"@firebase/app-types": "0.9.3", "@firebase/util": "1.11.0"}}, "@firebase/firestore": {"version": "4.7.10", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "@firebase/webchannel-wrapper": "1.0.3", "@grpc/grpc-js": "~1.9.0", "@grpc/proto-loader": "^0.7.8", "tslib": "^2.1.0"}}, "@firebase/firestore-compat": {"version": "0.3.45", "requires": {"@firebase/component": "0.6.13", "@firebase/firestore": "4.7.10", "@firebase/firestore-types": "3.0.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/firestore-types": {"version": "3.0.3", "requires": {}}, "@firebase/functions": {"version": "0.12.3", "requires": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/component": "0.6.13", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/functions-compat": {"version": "0.3.20", "requires": {"@firebase/component": "0.6.13", "@firebase/functions": "0.12.3", "@firebase/functions-types": "0.6.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/functions-types": {"version": "0.6.3"}, "@firebase/installations": {"version": "0.6.13", "requires": {"@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}}, "@firebase/installations-compat": {"version": "0.2.13", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/installations-types": "0.5.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/installations-types": {"version": "0.5.3", "requires": {}}, "@firebase/logger": {"version": "0.4.4", "requires": {"tslib": "^2.1.0"}}, "@firebase/messaging": {"version": "0.12.17", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/messaging-interop-types": "0.2.3", "@firebase/util": "1.11.0", "idb": "7.1.1", "tslib": "^2.1.0"}}, "@firebase/messaging-compat": {"version": "0.2.17", "requires": {"@firebase/component": "0.6.13", "@firebase/messaging": "0.12.17", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/messaging-interop-types": {"version": "0.2.3"}, "@firebase/performance": {"version": "0.7.2", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0", "web-vitals": "^4.2.4"}}, "@firebase/performance-compat": {"version": "0.2.15", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/performance": "0.7.2", "@firebase/performance-types": "0.2.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/performance-types": {"version": "0.2.3"}, "@firebase/remote-config": {"version": "0.6.0", "requires": {"@firebase/component": "0.6.13", "@firebase/installations": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/remote-config-compat": {"version": "0.2.13", "requires": {"@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/remote-config": "0.6.0", "@firebase/remote-config-types": "0.4.0", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/remote-config-types": {"version": "0.4.0"}, "@firebase/storage": {"version": "0.13.7", "requires": {"@firebase/component": "0.6.13", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/storage-compat": {"version": "0.3.17", "requires": {"@firebase/component": "0.6.13", "@firebase/storage": "0.13.7", "@firebase/storage-types": "0.8.3", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/storage-types": {"version": "0.8.3", "requires": {}}, "@firebase/util": {"version": "1.11.0", "requires": {"tslib": "^2.1.0"}}, "@firebase/vertexai": {"version": "1.2.1", "requires": {"@firebase/app-check-interop-types": "0.3.3", "@firebase/component": "0.6.13", "@firebase/logger": "0.4.4", "@firebase/util": "1.11.0", "tslib": "^2.1.0"}}, "@firebase/webchannel-wrapper": {"version": "1.0.3"}, "@grpc/grpc-js": {"version": "1.9.15", "requires": {"@grpc/proto-loader": "^0.7.8", "@types/node": ">=12.12.47"}}, "@grpc/proto-loader": {"version": "0.7.13", "requires": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}}, "@jridgewell/gen-mapping": {"version": "0.3.8", "devOptional": true, "peer": true, "requires": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}}, "@jridgewell/resolve-uri": {"version": "3.1.2", "devOptional": true, "peer": true}, "@jridgewell/set-array": {"version": "1.2.1", "devOptional": true, "peer": true}, "@jridgewell/source-map": {"version": "0.3.6", "devOptional": true, "peer": true, "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "@jridgewell/sourcemap-codec": {"version": "1.5.0"}, "@jridgewell/trace-mapping": {"version": "0.3.25", "devOptional": true, "peer": true, "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@mdi/font": {"version": "7.4.47"}, "@parcel/watcher": {"version": "2.5.1", "optional": true, "requires": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1", "detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "dependencies": {"detect-libc": {"version": "1.0.3", "optional": true}}}, "@parcel/watcher-win32-x64": {"version": "2.5.1", "optional": true}, "@protobufjs/aspromise": {"version": "1.1.2"}, "@protobufjs/base64": {"version": "1.1.2"}, "@protobufjs/codegen": {"version": "2.0.4"}, "@protobufjs/eventemitter": {"version": "1.1.0"}, "@protobufjs/fetch": {"version": "1.1.0", "requires": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "@protobufjs/float": {"version": "1.0.2"}, "@protobufjs/inquire": {"version": "1.1.0"}, "@protobufjs/path": {"version": "1.1.2"}, "@protobufjs/pool": {"version": "1.1.0"}, "@protobufjs/utf8": {"version": "1.1.0"}, "@rollup/rollup-win32-x64-msvc": {"version": "4.39.0", "optional": true}, "@tailwindcss/node": {"version": "4.1.3", "dev": true, "requires": {"enhanced-resolve": "^5.18.1", "jiti": "^2.4.2", "lightningcss": "1.29.2", "tailwindcss": "4.1.3"}}, "@tailwindcss/oxide": {"version": "4.1.3", "dev": true, "requires": {"@tailwindcss/oxide-android-arm64": "4.1.3", "@tailwindcss/oxide-darwin-arm64": "4.1.3", "@tailwindcss/oxide-darwin-x64": "4.1.3", "@tailwindcss/oxide-freebsd-x64": "4.1.3", "@tailwindcss/oxide-linux-arm-gnueabihf": "4.1.3", "@tailwindcss/oxide-linux-arm64-gnu": "4.1.3", "@tailwindcss/oxide-linux-arm64-musl": "4.1.3", "@tailwindcss/oxide-linux-x64-gnu": "4.1.3", "@tailwindcss/oxide-linux-x64-musl": "4.1.3", "@tailwindcss/oxide-win32-arm64-msvc": "4.1.3", "@tailwindcss/oxide-win32-x64-msvc": "4.1.3"}}, "@tailwindcss/oxide-win32-x64-msvc": {"version": "4.1.3", "dev": true, "optional": true}, "@tailwindcss/postcss": {"version": "4.1.3", "dev": true, "requires": {"@alloc/quick-lru": "^5.2.0", "@tailwindcss/node": "4.1.3", "@tailwindcss/oxide": "4.1.3", "postcss": "^8.4.41", "tailwindcss": "4.1.3"}}, "@types/eslint": {"version": "9.6.1", "dev": true, "peer": true, "requires": {"@types/estree": "*", "@types/json-schema": "*"}}, "@types/eslint-scope": {"version": "3.7.7", "dev": true, "peer": true, "requires": {"@types/eslint": "*", "@types/estree": "*"}}, "@types/estree": {"version": "1.0.7", "devOptional": true}, "@types/json-schema": {"version": "7.0.15", "dev": true, "peer": true}, "@types/node": {"version": "22.14.0", "requires": {"undici-types": "~6.21.0"}}, "@vitejs/plugin-vue": {"version": "5.2.3", "dev": true, "requires": {}}, "@vue/compiler-core": {"version": "3.5.13", "requires": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.13", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "@vue/compiler-dom": {"version": "3.5.13", "requires": {"@vue/compiler-core": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/compiler-sfc": {"version": "3.5.13", "requires": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.13", "@vue/compiler-dom": "3.5.13", "@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.48", "source-map-js": "^1.2.0"}}, "@vue/compiler-ssr": {"version": "3.5.13", "requires": {"@vue/compiler-dom": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/devtools-api": {"version": "7.7.2", "requires": {"@vue/devtools-kit": "^7.7.2"}}, "@vue/devtools-kit": {"version": "7.7.2", "requires": {"@vue/devtools-shared": "^7.7.2", "birpc": "^0.2.19", "hookable": "^5.5.3", "mitt": "^3.0.1", "perfect-debounce": "^1.0.0", "speakingurl": "^14.0.1", "superjson": "^2.2.1"}}, "@vue/devtools-shared": {"version": "7.7.2", "requires": {"rfdc": "^1.4.1"}}, "@vue/reactivity": {"version": "3.5.13", "requires": {"@vue/shared": "3.5.13"}}, "@vue/runtime-core": {"version": "3.5.13", "requires": {"@vue/reactivity": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/runtime-dom": {"version": "3.5.13", "requires": {"@vue/reactivity": "3.5.13", "@vue/runtime-core": "3.5.13", "@vue/shared": "3.5.13", "csstype": "^3.1.3"}}, "@vue/server-renderer": {"version": "3.5.13", "requires": {"@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13"}}, "@vue/shared": {"version": "3.5.13"}, "@vuetify/loader-shared": {"version": "2.1.0", "devOptional": true, "requires": {"upath": "^2.0.1"}}, "@webassemblyjs/ast": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/helper-api-error": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/helper-buffer": {"version": "1.14.1", "dev": true, "peer": true}, "@webassemblyjs/helper-numbers": {"version": "1.13.2", "dev": true, "peer": true, "requires": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "@webassemblyjs/ieee754": {"version": "1.13.2", "dev": true, "peer": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.13.2", "dev": true, "peer": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.13.2", "dev": true, "peer": true}, "@webassemblyjs/wasm-edit": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "@webassemblyjs/wasm-gen": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "@webassemblyjs/wasm-opt": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "@webassemblyjs/wasm-parser": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "@webassemblyjs/wast-printer": {"version": "1.14.1", "dev": true, "peer": true, "requires": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "dev": true, "peer": true}, "@xtuc/long": {"version": "4.2.2", "dev": true, "peer": true}, "acorn": {"version": "8.14.1", "devOptional": true, "peer": true}, "ajv": {"version": "8.17.1", "dev": true, "peer": true, "requires": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}}, "ajv-formats": {"version": "2.1.1", "dev": true, "peer": true, "requires": {"ajv": "^8.0.0"}}, "ajv-keywords": {"version": "5.1.0", "dev": true, "peer": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "ansi-regex": {"version": "5.0.1"}, "ansi-styles": {"version": "4.3.0", "requires": {"color-convert": "^2.0.1"}}, "autoprefixer": {"version": "10.4.21", "dev": true, "requires": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}}, "birpc": {"version": "0.2.19"}, "bootstrap-icons": {"version": "1.11.3"}, "braces": {"version": "3.0.3", "optional": true, "requires": {"fill-range": "^7.1.1"}}, "browserslist": {"version": "4.24.4", "dev": true, "requires": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}}, "buffer-from": {"version": "1.1.2", "devOptional": true, "peer": true}, "caniuse-lite": {"version": "1.0.30001712", "dev": true}, "chokidar": {"version": "4.0.3", "devOptional": true, "requires": {"readdirp": "^4.0.1"}}, "chrome-trace-event": {"version": "1.0.4", "dev": true, "peer": true}, "cliui": {"version": "8.0.1", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "color-convert": {"version": "2.0.1", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4"}, "commander": {"version": "2.20.3", "devOptional": true, "peer": true}, "copy-anything": {"version": "3.0.5", "requires": {"is-what": "^4.1.8"}}, "csstype": {"version": "3.1.3"}, "debug": {"version": "4.4.0", "devOptional": true, "requires": {"ms": "^2.1.3"}}, "detect-libc": {"version": "2.0.3", "devOptional": true}, "dotenv": {"version": "16.5.0"}, "electron-to-chromium": {"version": "1.5.134", "dev": true}, "emoji-regex": {"version": "8.0.0"}, "enhanced-resolve": {"version": "5.18.1", "dev": true, "requires": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}}, "entities": {"version": "4.5.0"}, "es-module-lexer": {"version": "1.6.0", "dev": true, "peer": true}, "esbuild": {"version": "0.25.2", "devOptional": true, "requires": {"@esbuild/aix-ppc64": "0.25.2", "@esbuild/android-arm": "0.25.2", "@esbuild/android-arm64": "0.25.2", "@esbuild/android-x64": "0.25.2", "@esbuild/darwin-arm64": "0.25.2", "@esbuild/darwin-x64": "0.25.2", "@esbuild/freebsd-arm64": "0.25.2", "@esbuild/freebsd-x64": "0.25.2", "@esbuild/linux-arm": "0.25.2", "@esbuild/linux-arm64": "0.25.2", "@esbuild/linux-ia32": "0.25.2", "@esbuild/linux-loong64": "0.25.2", "@esbuild/linux-mips64el": "0.25.2", "@esbuild/linux-ppc64": "0.25.2", "@esbuild/linux-riscv64": "0.25.2", "@esbuild/linux-s390x": "0.25.2", "@esbuild/linux-x64": "0.25.2", "@esbuild/netbsd-arm64": "0.25.2", "@esbuild/netbsd-x64": "0.25.2", "@esbuild/openbsd-arm64": "0.25.2", "@esbuild/openbsd-x64": "0.25.2", "@esbuild/sunos-x64": "0.25.2", "@esbuild/win32-arm64": "0.25.2", "@esbuild/win32-ia32": "0.25.2", "@esbuild/win32-x64": "0.25.2"}}, "escalade": {"version": "3.2.0"}, "eslint-scope": {"version": "5.1.1", "dev": true, "peer": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "esrecurse": {"version": "4.3.0", "dev": true, "peer": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "dev": true, "peer": true}}}, "estraverse": {"version": "4.3.0", "dev": true, "peer": true}, "estree-walker": {"version": "2.0.2"}, "events": {"version": "3.3.0", "dev": true, "peer": true}, "fast-deep-equal": {"version": "3.1.3", "dev": true, "peer": true}, "fast-uri": {"version": "3.0.6", "dev": true, "peer": true}, "faye-websocket": {"version": "0.11.4", "requires": {"websocket-driver": ">=0.5.1"}}, "fill-range": {"version": "7.1.1", "optional": true, "requires": {"to-regex-range": "^5.0.1"}}, "firebase": {"version": "11.6.0", "requires": {"@firebase/analytics": "0.10.12", "@firebase/analytics-compat": "0.2.18", "@firebase/app": "0.11.4", "@firebase/app-check": "0.8.13", "@firebase/app-check-compat": "0.3.20", "@firebase/app-compat": "0.2.53", "@firebase/app-types": "0.9.3", "@firebase/auth": "1.10.0", "@firebase/auth-compat": "0.5.20", "@firebase/data-connect": "0.3.3", "@firebase/database": "1.0.14", "@firebase/database-compat": "2.0.5", "@firebase/firestore": "4.7.10", "@firebase/firestore-compat": "0.3.45", "@firebase/functions": "0.12.3", "@firebase/functions-compat": "0.3.20", "@firebase/installations": "0.6.13", "@firebase/installations-compat": "0.2.13", "@firebase/messaging": "0.12.17", "@firebase/messaging-compat": "0.2.17", "@firebase/performance": "0.7.2", "@firebase/performance-compat": "0.2.15", "@firebase/remote-config": "0.6.0", "@firebase/remote-config-compat": "0.2.13", "@firebase/storage": "0.13.7", "@firebase/storage-compat": "0.3.17", "@firebase/util": "1.11.0", "@firebase/vertexai": "1.2.1"}}, "fraction.js": {"version": "4.3.7", "dev": true}, "get-caller-file": {"version": "2.0.5"}, "glob-to-regexp": {"version": "0.4.1", "dev": true, "peer": true}, "graceful-fs": {"version": "4.2.11", "dev": true}, "has-flag": {"version": "4.0.0", "dev": true, "peer": true}, "hookable": {"version": "5.5.3"}, "http-parser-js": {"version": "0.5.10"}, "idb": {"version": "7.1.1"}, "immutable": {"version": "5.1.1", "devOptional": true}, "is-extglob": {"version": "2.1.1", "optional": true}, "is-fullwidth-code-point": {"version": "3.0.0"}, "is-glob": {"version": "4.0.3", "optional": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "optional": true}, "is-what": {"version": "4.1.16"}, "jest-worker": {"version": "27.5.1", "dev": true, "peer": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}}, "jiti": {"version": "2.4.2", "devOptional": true}, "json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "peer": true}, "json-schema-traverse": {"version": "1.0.0", "dev": true, "peer": true}, "lightningcss": {"version": "1.29.2", "devOptional": true, "requires": {"detect-libc": "^2.0.3", "lightningcss-darwin-arm64": "1.29.2", "lightningcss-darwin-x64": "1.29.2", "lightningcss-freebsd-x64": "1.29.2", "lightningcss-linux-arm-gnueabihf": "1.29.2", "lightningcss-linux-arm64-gnu": "1.29.2", "lightningcss-linux-arm64-musl": "1.29.2", "lightningcss-linux-x64-gnu": "1.29.2", "lightningcss-linux-x64-musl": "1.29.2", "lightningcss-win32-arm64-msvc": "1.29.2", "lightningcss-win32-x64-msvc": "1.29.2"}}, "lightningcss-win32-x64-msvc": {"version": "1.29.2", "optional": true}, "loader-runner": {"version": "4.3.0", "dev": true, "peer": true}, "lodash.camelcase": {"version": "4.3.0"}, "long": {"version": "5.3.1"}, "magic-string": {"version": "0.30.17", "requires": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "merge-stream": {"version": "2.0.0", "dev": true, "peer": true}, "micromatch": {"version": "4.0.8", "optional": true, "requires": {"braces": "^3.0.3", "picomatch": "^2.3.1"}}, "mime-db": {"version": "1.52.0", "dev": true, "peer": true}, "mime-types": {"version": "2.1.35", "dev": true, "peer": true, "requires": {"mime-db": "1.52.0"}}, "mitt": {"version": "3.0.1"}, "ms": {"version": "2.1.3", "devOptional": true}, "nanoid": {"version": "3.3.11"}, "neo-async": {"version": "2.6.2", "dev": true}, "node-addon-api": {"version": "7.1.1", "optional": true}, "node-releases": {"version": "2.0.19", "dev": true}, "normalize-range": {"version": "0.1.2", "dev": true}, "perfect-debounce": {"version": "1.0.0"}, "picocolors": {"version": "1.1.1"}, "picomatch": {"version": "2.3.1", "optional": true}, "pinia": {"version": "3.0.1", "requires": {"@vue/devtools-api": "^7.7.2"}}, "postcss": {"version": "8.5.3", "requires": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "postcss-value-parser": {"version": "4.2.0", "dev": true}, "protobufjs": {"version": "7.4.0", "requires": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}}, "qrcode.vue": {"version": "3.6.0", "requires": {}}, "randombytes": {"version": "2.1.0", "dev": true, "peer": true, "requires": {"safe-buffer": "^5.1.0"}}, "readdirp": {"version": "4.1.2", "devOptional": true}, "require-directory": {"version": "2.1.1"}, "require-from-string": {"version": "2.0.2", "dev": true, "peer": true}, "rfdc": {"version": "1.4.1"}, "rollup": {"version": "4.39.0", "devOptional": true, "requires": {"@rollup/rollup-android-arm-eabi": "4.39.0", "@rollup/rollup-android-arm64": "4.39.0", "@rollup/rollup-darwin-arm64": "4.39.0", "@rollup/rollup-darwin-x64": "4.39.0", "@rollup/rollup-freebsd-arm64": "4.39.0", "@rollup/rollup-freebsd-x64": "4.39.0", "@rollup/rollup-linux-arm-gnueabihf": "4.39.0", "@rollup/rollup-linux-arm-musleabihf": "4.39.0", "@rollup/rollup-linux-arm64-gnu": "4.39.0", "@rollup/rollup-linux-arm64-musl": "4.39.0", "@rollup/rollup-linux-loongarch64-gnu": "4.39.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-musl": "4.39.0", "@rollup/rollup-linux-s390x-gnu": "4.39.0", "@rollup/rollup-linux-x64-gnu": "4.39.0", "@rollup/rollup-linux-x64-musl": "4.39.0", "@rollup/rollup-win32-arm64-msvc": "4.39.0", "@rollup/rollup-win32-ia32-msvc": "4.39.0", "@rollup/rollup-win32-x64-msvc": "4.39.0", "@types/estree": "1.0.7", "fsevents": "~2.3.2"}}, "safe-buffer": {"version": "5.2.1"}, "sass": {"version": "1.86.3", "devOptional": true, "requires": {"@parcel/watcher": "^2.4.1", "chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}}, "sass-loader": {"version": "13.3.3", "dev": true, "requires": {"neo-async": "^2.6.2"}}, "schema-utils": {"version": "4.3.0", "dev": true, "peer": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}}, "serialize-javascript": {"version": "6.0.2", "dev": true, "peer": true, "requires": {"randombytes": "^2.1.0"}}, "source-map": {"version": "0.6.1", "devOptional": true, "peer": true}, "source-map-js": {"version": "1.2.1"}, "source-map-support": {"version": "0.5.21", "devOptional": true, "peer": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "speakingurl": {"version": "14.0.1"}, "string-width": {"version": "4.2.3", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "requires": {"ansi-regex": "^5.0.1"}}, "superjson": {"version": "2.2.2", "requires": {"copy-anything": "^3.0.2"}}, "supports-color": {"version": "8.1.1", "dev": true, "peer": true, "requires": {"has-flag": "^4.0.0"}}, "tailwindcss": {"version": "4.1.3", "dev": true}, "tapable": {"version": "2.2.1", "dev": true}, "terser": {"version": "5.39.0", "devOptional": true, "peer": true, "requires": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}}, "terser-webpack-plugin": {"version": "5.3.14", "dev": true, "peer": true, "requires": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}}, "to-regex-range": {"version": "5.0.1", "optional": true, "requires": {"is-number": "^7.0.0"}}, "tslib": {"version": "2.8.1"}, "undici-types": {"version": "6.21.0"}, "upath": {"version": "2.0.1", "devOptional": true}, "update-browserslist-db": {"version": "1.1.3", "dev": true, "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}}, "uuid": {"version": "11.1.0"}, "vite": {"version": "6.2.5", "devOptional": true, "requires": {"esbuild": "^0.25.0", "fsevents": "~2.3.3", "postcss": "^8.5.3", "rollup": "^4.30.1"}}, "vite-plugin-vuetify": {"version": "2.1.1", "devOptional": true, "requires": {"@vuetify/loader-shared": "^2.1.0", "debug": "^4.3.3", "upath": "^2.0.1"}}, "vue": {"version": "3.5.13", "requires": {"@vue/compiler-dom": "3.5.13", "@vue/compiler-sfc": "3.5.13", "@vue/runtime-dom": "3.5.13", "@vue/server-renderer": "3.5.13", "@vue/shared": "3.5.13"}}, "vue-router": {"version": "4.5.0", "requires": {"@vue/devtools-api": "^6.6.4"}, "dependencies": {"@vue/devtools-api": {"version": "6.6.4"}}}, "vuetify": {"version": "3.8.1", "requires": {}}, "watchpack": {"version": "2.4.2", "dev": true, "peer": true, "requires": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}}, "web-vitals": {"version": "4.2.4"}, "webfontloader": {"version": "1.6.28"}, "webpack": {"version": "5.99.3", "dev": true, "peer": true, "requires": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}}, "webpack-sources": {"version": "3.2.3", "dev": true, "peer": true}, "websocket-driver": {"version": "0.7.4", "requires": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4"}, "wrap-ansi": {"version": "7.0.0", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "y18n": {"version": "5.0.8"}, "yargs": {"version": "17.7.2", "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1"}}}, "ansi-regex": {"version": "5.0.1"}, "ansi-styles": {"version": "4.3.0", "requires": {"color-convert": "^2.0.1"}}, "autoprefixer": {"version": "10.4.21", "dev": true, "requires": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}}, "birpc": {"version": "0.2.19"}, "bootstrap-icons": {"version": "1.11.3"}, "braces": {"version": "3.0.3", "optional": true, "requires": {"fill-range": "^7.1.1"}}, "browserslist": {"version": "4.24.4", "dev": true, "requires": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}}, "buffer-from": {"version": "1.1.2", "devOptional": true, "peer": true}, "caniuse-lite": {"version": "1.0.30001712", "dev": true}, "chokidar": {"version": "4.0.3", "devOptional": true, "requires": {"readdirp": "^4.0.1"}}, "chrome-trace-event": {"version": "1.0.4", "dev": true, "peer": true}, "cliui": {"version": "8.0.1", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "color-convert": {"version": "2.0.1", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4"}, "commander": {"version": "2.20.3", "devOptional": true, "peer": true}, "copy-anything": {"version": "3.0.5", "requires": {"is-what": "^4.1.8"}}, "csstype": {"version": "3.1.3"}, "debug": {"version": "4.4.0", "devOptional": true, "requires": {"ms": "^2.1.3"}}, "detect-libc": {"version": "2.0.3", "devOptional": true}, "dotenv": {"version": "16.5.0"}, "electron-to-chromium": {"version": "1.5.134", "dev": true}, "emoji-regex": {"version": "8.0.0"}, "enhanced-resolve": {"version": "5.18.1", "dev": true, "requires": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}}, "entities": {"version": "4.5.0"}, "es-module-lexer": {"version": "1.6.0", "dev": true, "peer": true}, "esbuild": {"version": "0.25.2", "devOptional": true, "requires": {"@esbuild/aix-ppc64": "0.25.2", "@esbuild/android-arm": "0.25.2", "@esbuild/android-arm64": "0.25.2", "@esbuild/android-x64": "0.25.2", "@esbuild/darwin-arm64": "0.25.2", "@esbuild/darwin-x64": "0.25.2", "@esbuild/freebsd-arm64": "0.25.2", "@esbuild/freebsd-x64": "0.25.2", "@esbuild/linux-arm": "0.25.2", "@esbuild/linux-arm64": "0.25.2", "@esbuild/linux-ia32": "0.25.2", "@esbuild/linux-loong64": "0.25.2", "@esbuild/linux-mips64el": "0.25.2", "@esbuild/linux-ppc64": "0.25.2", "@esbuild/linux-riscv64": "0.25.2", "@esbuild/linux-s390x": "0.25.2", "@esbuild/linux-x64": "0.25.2", "@esbuild/netbsd-arm64": "0.25.2", "@esbuild/netbsd-x64": "0.25.2", "@esbuild/openbsd-arm64": "0.25.2", "@esbuild/openbsd-x64": "0.25.2", "@esbuild/sunos-x64": "0.25.2", "@esbuild/win32-arm64": "0.25.2", "@esbuild/win32-ia32": "0.25.2", "@esbuild/win32-x64": "0.25.2"}}, "escalade": {"version": "3.2.0"}, "eslint-scope": {"version": "5.1.1", "dev": true, "peer": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "esrecurse": {"version": "4.3.0", "dev": true, "peer": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "dev": true, "peer": true}}}, "estraverse": {"version": "4.3.0", "dev": true, "peer": true}, "estree-walker": {"version": "2.0.2"}, "events": {"version": "3.3.0", "dev": true, "peer": true}, "fast-deep-equal": {"version": "3.1.3", "dev": true, "peer": true}, "fast-uri": {"version": "3.0.6", "dev": true, "peer": true}, "faye-websocket": {"version": "0.11.4", "requires": {"websocket-driver": ">=0.5.1"}}, "fill-range": {"version": "7.1.1", "optional": true, "requires": {"to-regex-range": "^5.0.1"}}, "firebase": {"version": "11.6.0", "requires": {"@firebase/analytics": "0.10.12", "@firebase/analytics-compat": "0.2.18", "@firebase/app": "0.11.4", "@firebase/app-check": "0.8.13", "@firebase/app-check-compat": "0.3.20", "@firebase/app-compat": "0.2.53", "@firebase/app-types": "0.9.3", "@firebase/auth": "1.10.0", "@firebase/auth-compat": "0.5.20", "@firebase/data-connect": "0.3.3", "@firebase/database": "1.0.14", "@firebase/database-compat": "2.0.5", "@firebase/firestore": "4.7.10", "@firebase/firestore-compat": "0.3.45", "@firebase/functions": "0.12.3", "@firebase/functions-compat": "0.3.20", "@firebase/installations": "0.6.13", "@firebase/installations-compat": "0.2.13", "@firebase/messaging": "0.12.17", "@firebase/messaging-compat": "0.2.17", "@firebase/performance": "0.7.2", "@firebase/performance-compat": "0.2.15", "@firebase/remote-config": "0.6.0", "@firebase/remote-config-compat": "0.2.13", "@firebase/storage": "0.13.7", "@firebase/storage-compat": "0.3.17", "@firebase/util": "1.11.0", "@firebase/vertexai": "1.2.1"}}, "fraction.js": {"version": "4.3.7", "dev": true}, "get-caller-file": {"version": "2.0.5"}, "glob-to-regexp": {"version": "0.4.1", "dev": true, "peer": true}, "graceful-fs": {"version": "4.2.11", "dev": true}, "has-flag": {"version": "4.0.0", "dev": true, "peer": true}, "hookable": {"version": "5.5.3"}, "http-parser-js": {"version": "0.5.10"}, "idb": {"version": "7.1.1"}, "immutable": {"version": "5.1.1", "devOptional": true}, "is-extglob": {"version": "2.1.1", "optional": true}, "is-fullwidth-code-point": {"version": "3.0.0"}, "is-glob": {"version": "4.0.3", "optional": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "optional": true}, "is-what": {"version": "4.1.16"}, "jest-worker": {"version": "27.5.1", "dev": true, "peer": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}}, "jiti": {"version": "2.4.2", "devOptional": true}, "json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "peer": true}, "json-schema-traverse": {"version": "1.0.0", "dev": true, "peer": true}, "lightningcss": {"version": "1.29.2", "devOptional": true, "requires": {"detect-libc": "^2.0.3", "lightningcss-darwin-arm64": "1.29.2", "lightningcss-darwin-x64": "1.29.2", "lightningcss-freebsd-x64": "1.29.2", "lightningcss-linux-arm-gnueabihf": "1.29.2", "lightningcss-linux-arm64-gnu": "1.29.2", "lightningcss-linux-arm64-musl": "1.29.2", "lightningcss-linux-x64-gnu": "1.29.2", "lightningcss-linux-x64-musl": "1.29.2", "lightningcss-win32-arm64-msvc": "1.29.2", "lightningcss-win32-x64-msvc": "1.29.2"}}, "lightningcss-win32-x64-msvc": {"version": "1.29.2", "optional": true}, "loader-runner": {"version": "4.3.0", "dev": true, "peer": true}, "lodash.camelcase": {"version": "4.3.0"}, "long": {"version": "5.3.1"}, "magic-string": {"version": "0.30.17", "requires": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "merge-stream": {"version": "2.0.0", "dev": true, "peer": true}, "micromatch": {"version": "4.0.8", "optional": true, "requires": {"braces": "^3.0.3", "picomatch": "^2.3.1"}}, "mime-db": {"version": "1.52.0", "dev": true, "peer": true}, "mime-types": {"version": "2.1.35", "dev": true, "peer": true, "requires": {"mime-db": "1.52.0"}}, "mitt": {"version": "3.0.1"}, "ms": {"version": "2.1.3", "devOptional": true}, "nanoid": {"version": "3.3.11"}, "neo-async": {"version": "2.6.2", "dev": true}, "node-addon-api": {"version": "7.1.1", "optional": true}, "node-releases": {"version": "2.0.19", "dev": true}, "normalize-range": {"version": "0.1.2", "dev": true}, "perfect-debounce": {"version": "1.0.0"}, "picocolors": {"version": "1.1.1"}, "picomatch": {"version": "2.3.1", "optional": true}, "pinia": {"version": "3.0.1", "requires": {"@vue/devtools-api": "^7.7.2"}}, "postcss": {"version": "8.5.3", "requires": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "postcss-value-parser": {"version": "4.2.0", "dev": true}, "protobufjs": {"version": "7.4.0", "requires": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}}, "qrcode.vue": {"version": "3.6.0", "requires": {}}, "randombytes": {"version": "2.1.0", "dev": true, "peer": true, "requires": {"safe-buffer": "^5.1.0"}}, "readdirp": {"version": "4.1.2", "devOptional": true}, "require-directory": {"version": "2.1.1"}, "require-from-string": {"version": "2.0.2", "dev": true, "peer": true}, "rfdc": {"version": "1.4.1"}, "rollup": {"version": "4.39.0", "devOptional": true, "requires": {"@rollup/rollup-android-arm-eabi": "4.39.0", "@rollup/rollup-android-arm64": "4.39.0", "@rollup/rollup-darwin-arm64": "4.39.0", "@rollup/rollup-darwin-x64": "4.39.0", "@rollup/rollup-freebsd-arm64": "4.39.0", "@rollup/rollup-freebsd-x64": "4.39.0", "@rollup/rollup-linux-arm-gnueabihf": "4.39.0", "@rollup/rollup-linux-arm-musleabihf": "4.39.0", "@rollup/rollup-linux-arm64-gnu": "4.39.0", "@rollup/rollup-linux-arm64-musl": "4.39.0", "@rollup/rollup-linux-loongarch64-gnu": "4.39.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-musl": "4.39.0", "@rollup/rollup-linux-s390x-gnu": "4.39.0", "@rollup/rollup-linux-x64-gnu": "4.39.0", "@rollup/rollup-linux-x64-musl": "4.39.0", "@rollup/rollup-win32-arm64-msvc": "4.39.0", "@rollup/rollup-win32-ia32-msvc": "4.39.0", "@rollup/rollup-win32-x64-msvc": "4.39.0", "@types/estree": "1.0.7", "fsevents": "~2.3.2"}}, "safe-buffer": {"version": "5.2.1"}, "sass": {"version": "1.86.3", "devOptional": true, "requires": {"@parcel/watcher": "^2.4.1", "chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}}, "sass-loader": {"version": "13.3.3", "dev": true, "requires": {"neo-async": "^2.6.2"}}, "schema-utils": {"version": "4.3.0", "dev": true, "peer": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}}, "serialize-javascript": {"version": "6.0.2", "dev": true, "peer": true, "requires": {"randombytes": "^2.1.0"}}, "source-map": {"version": "0.6.1", "devOptional": true, "peer": true}, "source-map-js": {"version": "1.2.1"}, "source-map-support": {"version": "0.5.21", "devOptional": true, "peer": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "speakingurl": {"version": "14.0.1"}, "string-width": {"version": "4.2.3", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "requires": {"ansi-regex": "^5.0.1"}}, "superjson": {"version": "2.2.2", "requires": {"copy-anything": "^3.0.2"}}, "supports-color": {"version": "8.1.1", "dev": true, "peer": true, "requires": {"has-flag": "^4.0.0"}}, "tailwindcss": {"version": "4.1.3", "dev": true}, "tapable": {"version": "2.2.1", "dev": true}, "terser": {"version": "5.39.0", "devOptional": true, "peer": true, "requires": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}}, "terser-webpack-plugin": {"version": "5.3.14", "dev": true, "peer": true, "requires": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}}, "to-regex-range": {"version": "5.0.1", "optional": true, "requires": {"is-number": "^7.0.0"}}, "tslib": {"version": "2.8.1"}, "undici-types": {"version": "6.21.0"}, "upath": {"version": "2.0.1", "devOptional": true}, "update-browserslist-db": {"version": "1.1.3", "dev": true, "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}}, "uuid": {"version": "11.1.0"}, "vite": {"version": "6.2.5", "devOptional": true, "requires": {"esbuild": "^0.25.0", "fsevents": "~2.3.3", "postcss": "^8.5.3", "rollup": "^4.30.1"}}, "vite-plugin-vuetify": {"version": "2.1.1", "devOptional": true, "requires": {"@vuetify/loader-shared": "^2.1.0", "debug": "^4.3.3", "upath": "^2.0.1"}}, "vue": {"version": "3.5.13", "requires": {"@vue/compiler-dom": "3.5.13", "@vue/compiler-sfc": "3.5.13", "@vue/runtime-dom": "3.5.13", "@vue/server-renderer": "3.5.13", "@vue/shared": "3.5.13"}}, "vue-router": {"version": "4.5.0", "requires": {"@vue/devtools-api": "^6.6.4"}, "dependencies": {"@vue/devtools-api": {"version": "6.6.4"}}}, "vuetify": {"version": "3.8.1", "requires": {}}, "watchpack": {"version": "2.4.2", "dev": true, "peer": true, "requires": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}}, "web-vitals": {"version": "4.2.4"}, "webfontloader": {"version": "1.6.28"}, "webpack": {"version": "5.99.3", "dev": true, "peer": true, "requires": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}}, "webpack-sources": {"version": "3.2.3", "dev": true, "peer": true}, "websocket-driver": {"version": "0.7.4", "requires": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4"}, "wrap-ansi": {"version": "7.0.0", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "y18n": {"version": "5.0.8"}, "yargs": {"version": "17.7.2", "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1"}}}