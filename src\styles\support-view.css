.support-view {
  min-height: 100vh;
  background: #121212;
  color: #ffffff;
  padding: 20px;
}

.support-container {
  max-width: 1200px;
  margin: 0 auto;
}

.support-header {
  text-align: center;
  margin-bottom: 40px;
}

.support-header h1 {
  font-size: 2.5rem;
  margin-bottom: 12px;
  color: #00cccc;
}

.support-header p {
  font-size: 1.1rem;
  color: #ccc;
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: #333;
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
}

.stat-number {
  margin: 0;
  color: #00cccc;
  font-size: 2rem;
  font-weight: bold;
}

.filters-section {
  margin-bottom: 30px;
}

.filters-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 12px 16px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.9rem;
  min-width: 180px;
}

.filter-select:focus {
  outline: none;
  border-color: #00cccc;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.9rem;
}

.search-input:focus {
  outline: none;
  border-color: #00cccc;
}

.loading-state, .empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #888;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #00cccc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 1.3rem;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 1rem;
}

.tickets-table-container {
  background: #2a2a2a;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #444;
}

.tickets-table {
  width: 100%;
  border-collapse: collapse;
}

.tickets-table th {
  background: #333;
  color: #ffffff;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 0.9rem;
  border-bottom: 1px solid #444;
}

.tickets-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #444;
  vertical-align: top;
}

.ticket-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.ticket-number {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #00cccc;
  font-size: 0.9rem;
}

.subject-content h4 {
  margin: 0 0 4px 0;
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
}

.ticket-preview {
  margin: 0;
  color: #888;
  font-size: 0.8rem;
  line-height: 1.4;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-blue {
  background: rgba(28, 148, 224, 0.2);
  color: #1c94e0;
  border: 1px solid rgba(28, 148, 224, 0.3);
}

.status-yellow {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-green {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-gray {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.ticket-date, .ticket-updated {
  color: #888;
  font-size: 0.8rem;
  white-space: nowrap;
}

.btn-icon {
  background: none;
  border: none;
  color: #888;
  font-size: 1rem;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #00cccc;
}

.btn {
  padding: 12px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-primary {
  background: #00cccc;
  color: #121212;
}

.btn-primary:hover {
  background: #1c94e0;
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: #00cccc;
  border: 1px solid #00cccc;
}

.btn-ghost:hover {
  background: rgba(0, 204, 204, 0.1);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: #1e1e1e;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-content.large {
  max-width: 800px;
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
  margin-bottom: 24px;
}

.modal-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.ticket-details {
  padding: 0 24px 24px;
}

.ticket-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 8px;
}

.info-label {
  color: #888;
  font-size: 0.9rem;
  font-weight: 500;
}

.info-value {
  color: #ffffff;
  font-size: 0.9rem;
}

.ticket-message, .admin-response {
  margin-bottom: 24px;
}

.ticket-message h4, .admin-response h4 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

.message-content, .response-content {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
  color: #ccc;
  line-height: 1.6;
  white-space: pre-wrap;
}

.admin-response .response-content {
  background: rgba(0, 204, 204, 0.05);
  border-color: rgba(0, 204, 204, 0.2);
}

.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1001;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .support-header h1 {
    font-size: 2rem;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .tickets-table-container {
    overflow-x: auto;
  }
  
  .tickets-table {
    min-width: 800px;
  }
  
  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .ticket-info-grid {
    grid-template-columns: 1fr;
  }
}
