<template>
  <v-dialog v-model="show" persistent max-width="800px" class="user-info-modal">
    <v-card class="modal-card">
      <!-- Header -->
      <v-card-title class="modal-header">
        <div class="header-content">
          <h2>👋 ¡Bienvenido a De Una!</h2>
          <p class="subtitle">Completa tu información para comenzar</p>
        </div>
      </v-card-title>

      <!-- Progress indicator -->
      <div class="progress-container">
        <v-progress-linear 
          :model-value="progressPercentage" 
          color="turquesa" 
          height="4"
          class="progress-bar"
        />
        <p class="progress-text">{{ progressPercentage }}% completado</p>
      </div>

      <!-- Loading overlay -->
      <v-overlay v-if="submitting" contained class="loading-overlay">
        <div class="loading-content">
          <v-progress-circular 
            indeterminate 
            color="turquesa" 
            size="64"
            class="loading-spinner"
          />
          <p class="loading-text">{{ loadingMessage }}</p>
        </div>
      </v-overlay>

      <!-- Form content -->
      <v-card-text class="modal-content">
        <v-form ref="formRef" v-model="formValid" @submit.prevent="handleSubmit">
          <!-- Información Personal -->
          <div class="form-section">
            <h3 class="section-title">👤 Información Personal</h3>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.nombre"
                  label="Nombre *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="comfortable"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.apellido"
                  label="Apellido *"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="comfortable"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.rut"
                  label="RUT/ID *"
                  :rules="[rules.required, rules.rut]"
                  variant="outlined"
                  density="comfortable"
                  hint="Formato: 12.345.678-9"
                  @input="formatRut"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.telefono"
                  label="Teléfono *"
                  :rules="[rules.required, rules.phone]"
                  variant="outlined"
                  density="comfortable"
                  hint="Incluye código de país: +56912345678"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.fechaNacimiento"
                  label="Fecha de Nacimiento *"
                  type="date"
                  :rules="[rules.required, rules.age]"
                  variant="outlined"
                  density="comfortable"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="form.sexo"
                  label="Sexo *"
                  :items="sexOptions"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="comfortable"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="form.empresa"
                  label="Nombre de Empresa (Opcional)"
                  variant="outlined"
                  density="comfortable"
                  hint="Si tienes un negocio o empresa"
                />
              </v-col>
            </v-row>
          </div>

          <!-- Información Geográfica -->
          <div class="form-section">
            <h3 class="section-title">🌍 Ubicación</h3>
            <GeoSelector 
              v-model="geoData" 
              :required="true"
              @update:modelValue="updateGeoData"
            />
          </div>

          <!-- Información de Seguridad -->
          <div class="form-section">
            <h3 class="section-title">🔒 Información de Seguridad</h3>
            <v-alert 
              type="info" 
              variant="tonal" 
              class="security-info"
            >
              <v-icon>mdi-shield-check</v-icon>
              <div>
                <strong>¿Por qué necesitamos esta información?</strong>
                <ul class="info-list">
                  <li>Tu RUT/ID nos ayuda a verificar tu identidad</li>
                  <li>Tu teléfono es necesario para notificaciones de seguridad</li>
                  <li>Tu ubicación nos permite cumplir con regulaciones locales</li>
                </ul>
              </div>
            </v-alert>
          </div>

          <!-- Términos y Condiciones -->
          <div class="form-section">
            <v-checkbox
              v-model="form.acceptTerms"
              :rules="[rules.required]"
              color="turquesa"
              class="terms-checkbox"
            >
              <template #label>
                <span class="terms-text">
                  Acepto los 
                  <a href="#" @click.prevent="showTerms = true" class="terms-link">
                    Términos y Condiciones
                  </a> 
                  y la 
                  <a href="#" @click.prevent="showPrivacy = true" class="terms-link">
                    Política de Privacidad
                  </a>
                  *
                </span>
              </template>
            </v-checkbox>
          </div>
        </v-form>
      </v-card-text>

      <!-- Actions -->
      <v-card-actions class="modal-actions">
        <v-spacer />
        <v-btn
          @click="handleSubmit"
          :disabled="!formValid || submitting"
          :loading="submitting"
          color="turquesa"
          variant="elevated"
          size="large"
          class="submit-btn"
        >
          <v-icon left>mdi-check</v-icon>
          Completar Registro
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- Terms Modal -->
    <v-dialog v-model="showTerms" max-width="600px">
      <v-card>
        <v-card-title>Términos y Condiciones</v-card-title>
        <v-card-text>
          <p>Aquí irían los términos y condiciones completos...</p>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showTerms = false" color="turquesa">Cerrar</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Privacy Modal -->
    <v-dialog v-model="showPrivacy" max-width="600px">
      <v-card>
        <v-card-title>Política de Privacidad</v-card-title>
        <v-card-text>
          <p>Aquí iría la política de privacidad completa...</p>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showPrivacy = false" color="turquesa">Cerrar</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useAuthStore } from '@/store/auth'
import GeoSelector from '@/components/forms/GeoSelector.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'completed', 'error'])

// Store
const authStore = useAuthStore()

// Reactive data
const show = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref(null)
const formValid = ref(false)
const submitting = ref(false)
const showTerms = ref(false)
const showPrivacy = ref(false)

// Loading messages
const loadingMessages = [
  "Configurando tu cuenta en Maiko Studios...",
  "Validando tu información...",
  "Creando tu perfil personalizado...",
  "Un momento, estamos personalizando tu experiencia..."
]
const loadingMessage = ref(loadingMessages[0])

// Form data
const form = ref({
  nombre: '',
  apellido: '',
  rut: '',
  telefono: '',
  fechaNacimiento: '',
  sexo: '',
  empresa: '',
  acceptTerms: false
})

const geoData = ref({
  pais: '',
  region: '',
  provincia: '',
  comuna: ''
})

// Options
const sexOptions = [
  { title: 'Masculino', value: 'masculino' },
  { title: 'Femenino', value: 'femenino' },
  { title: 'Otro', value: 'otro' },
  { title: 'Prefiero no decir', value: 'no_especifica' }
]

// Validation rules
const rules = {
  required: (value) => !!value || 'Este campo es obligatorio',
  rut: (value) => {
    if (!value) return true
    // Validación básica de RUT chileno
    const rutRegex = /^[0-9]+[-|‐]{1}[0-9kK]{1}$/
    return rutRegex.test(value.replace(/\./g, '')) || 'Formato de RUT inválido'
  },
  phone: (value) => {
    if (!value) return true
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    return phoneRegex.test(value) || 'Formato de teléfono inválido'
  },
  age: (value) => {
    if (!value) return true
    const birthDate = new Date(value)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()
    return age >= 18 || 'Debes ser mayor de 18 años'
  }
}

// Computed
const progressPercentage = computed(() => {
  const fields = [
    form.value.nombre,
    form.value.apellido,
    form.value.rut,
    form.value.telefono,
    form.value.fechaNacimiento,
    form.value.sexo,
    geoData.value.pais,
    geoData.value.region,
    geoData.value.comuna,
    form.value.acceptTerms
  ]
  
  const completedFields = fields.filter(field => !!field).length
  return Math.round((completedFields / fields.length) * 100)
})

// Methods
const formatRut = () => {
  // Formatear RUT chileno automáticamente
  let rut = form.value.rut.replace(/[^0-9kK]/g, '')
  if (rut.length > 1) {
    rut = rut.slice(0, -1).replace(/\B(?=(\d{3})+(?!\d))/g, '.') + '-' + rut.slice(-1)
  }
  form.value.rut = rut
}

const updateGeoData = (newGeoData) => {
  geoData.value = newGeoData
}

const handleSubmit = async () => {
  if (!formValid.value) {
    await nextTick()
    formRef.value?.validate()
    return
  }

  submitting.value = true
  
  // Simular mensajes de carga
  let messageIndex = 0
  const messageInterval = setInterval(() => {
    messageIndex = (messageIndex + 1) % loadingMessages.length
    loadingMessage.value = loadingMessages[messageIndex]
  }, 1500)

  try {
    // Aquí se llamaría a la Cloud Function para completar el onboarding
    console.log('📝 Enviando información del usuario:', {
      ...form.value,
      ...geoData.value
    })
    
    // Simular delay de procesamiento
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    emit('completed', {
      ...form.value,
      ...geoData.value
    })
    
  } catch (error) {
    console.error('❌ Error completando onboarding:', error)
    emit('error', error)
  } finally {
    clearInterval(messageInterval)
    submitting.value = false
  }
}

// Pre-fill form with Google data
watch(() => authStore.user, (user) => {
  if (user && user.displayName) {
    const nameParts = user.displayName.split(' ')
    form.value.nombre = nameParts[0] || ''
    form.value.apellido = nameParts.slice(1).join(' ') || ''
  }
}, { immediate: true })
</script>

<style scoped>
.user-info-modal .modal-card {
  background: var(--color-surface);
  color: var(--color-text);
}

.modal-header {
  background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
  color: white;
  padding: 2rem;
}

.header-content h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.subtitle {
  opacity: 0.9;
  font-size: 1.1rem;
}

.progress-container {
  padding: 1rem 2rem 0;
}

.progress-text {
  text-align: center;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.modal-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 2rem;
}

.section-title {
  color: var(--color-turquesa);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.security-info {
  margin: 1rem 0;
}

.info-list {
  margin: 0.5rem 0 0 1rem;
}

.terms-checkbox {
  margin-top: 1rem;
}

.terms-text {
  font-size: 0.9rem;
}

.terms-link {
  color: var(--color-turquesa);
  text-decoration: underline;
}

.modal-actions {
  padding: 1.5rem 2rem;
  background: var(--color-surface-variant);
}

.submit-btn {
  min-width: 200px;
}

.loading-overlay {
  background: rgba(18, 18, 18, 0.9);
  backdrop-filter: blur(4px);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive */
@media (max-width: 768px) {
  .modal-content {
    padding: 1rem;
    max-height: 70vh;
  }
  
  .modal-header {
    padding: 1.5rem;
  }
  
  .header-content h2 {
    font-size: 1.5rem;
  }
}
</style>
