/* Admin Panel Layout Optimizations */

/* Contenedor principal del panel admin */
.admin-panel {
  display: flex;
  min-height: 100vh;
  background: var(--color-background);
  font-family: var(--font-primary);
  position: relative;
  overflow: hidden;
}

/* Contenido principal responsive */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 280px;
  padding: 1.5rem;
  width: calc(100% - 280px);
  min-height: calc(100vh - 80px);
  transition: margin-left 0.3s ease, width 0.3s ease;
  max-width: 100%;
}

.main-content.sidebar-collapsed {
  margin-left: 70px;
  width: calc(100% - 70px);
}

/* Optimizaciones para tablas en el contenido principal */
.main-content .table-container {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
}

.main-content table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  background: var(--color-surface);
}

/* Headers de sección optimizados */
.main-content .header-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.main-content .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.main-content .page-title {
  margin: 0;
  color: var(--color-text);
  font-size: 2rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-content .page-description {
  margin: 0.5rem 0 0 0;
  color: var(--color-text-secondary);
  font-size: 1rem;
}

/* Botones de acción en headers */
.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn-refresh {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-refresh:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-refresh:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-refresh .spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Estadísticas y métricas */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-turquesa);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--color-turquesa);
  margin: 0;
}

.stat-label {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filtros y búsqueda */
.filters-section {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filters-row {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 1rem;
  align-items: center;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  color: var(--color-text);
  font-size: 0.9rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--color-turquesa);
  box-shadow: 0 0 0 3px rgba(0, 204, 204, 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .main-content,
  .main-content.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
    padding: 0.75rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .page-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .main-content {
    padding: 0.5rem;
  }
}

/* Mejoras de accesibilidad */
@media (prefers-reduced-motion: reduce) {
  .main-content,
  .stat-card,
  .btn-refresh {
    transition: none;
  }
  
  .spin {
    animation: none;
  }
}

/* Tema de alto contraste */
@media (prefers-contrast: high) {
  .main-content .table-container {
    border: 2px solid var(--color-border);
  }
  
  .stat-card {
    border-width: 2px;
  }
}
