/* Estilos para formularios del panel de administrador */

.header-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #1e1e1e, #2a2a2a);
  border-radius: 16px;
  border: 1px solid #444;
}

.header-section h2 {
  margin: 0 0 12px 0;
  color: #00cccc;
  font-size: 2rem;
  font-weight: 600;
}

.header-section p {
  margin: 0;
  color: #ccc;
  font-size: 1.1rem;
}

.form-container {
  background: #1e1e1e;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid #444;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.form-container h3 {
  margin: 0 0 24px 0;
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #00cccc;
  padding-bottom: 12px;
}

.support-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background: #2a2a2a;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #444;
}

.form-section h4 {
  margin: 0 0 20px 0;
  color: #00cccc;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #ffffff;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  background: #1e1e1e;
  border: 1px solid #555;
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #00cccc;
  box-shadow: 0 0 0 3px rgba(0, 204, 204, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #888;
}

.form-group small {
  color: #888;
  font-size: 0.8rem;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #444;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #00cccc, #1c94e0);
  color: #121212;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1c94e0, #00cccc);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 204, 204, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: #ffffff;
  border: 1px solid #6c757d;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  border-color: #5a6268;
  transform: translateY(-1px);
}

.info-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.info-card {
  background: #1e1e1e;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #444;
}

.info-card h4 {
  margin: 0 0 16px 0;
  color: #00cccc;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-card li {
  color: #ccc;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.info-card li::before {
  content: "•";
  color: #00cccc;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.info-card li strong {
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-container {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .info-section {
    grid-template-columns: 1fr;
  }
  
  .header-section {
    padding: 16px;
  }
  
  .header-section h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 16px;
  }
  
  .form-container h3 {
    font-size: 1.2rem;
  }
  
  .form-section h4 {
    font-size: 1rem;
  }
}

/* Animaciones */
.form-container {
  animation: fadeInUp 0.6s ease-out;
}

.form-section {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Estados de validación */
.form-group input.error,
.form-group select.error {
  border-color: #ff4757;
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.form-group input.success,
.form-group select.success {
  border-color: #28a745;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.error-message {
  color: #ff4757;
  font-size: 0.8rem;
  margin-top: 4px;
}

.success-message {
  color: #28a745;
  font-size: 0.8rem;
  margin-top: 4px;
}
