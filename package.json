{"name": "altoque-vue-mvp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "lint": "echo '<PERSON><PERSON> passed - no linter configured yet'"}, "dependencies": {"@mdi/font": "^7.4.47", "altoque-vue-mvp": "file:", "bootstrap-icons": "^1.11.3", "dotenv": "^16.5.0", "firebase": "^11.6.0", "pinia": "^3.0.1", "qrcode.vue": "^3.6.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.1", "webfontloader": "^1.6.28"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "@pinia/testing": "^0.1.6", "jsdom": "^25.0.1", "sass": "^1.86.3", "sass-loader": "^13.3.3", "vite": "^6.2.0", "vite-plugin-vuetify": "^2.1.1", "vitest": "^2.1.8"}}