/* Admin Users Management Styles */

/* Contenedor principal */
.users-list {
    padding: 1.5rem;
    max-width: 100%;
    overflow-x: hidden;
}

/* Header */
.management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--color-surface);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
}

.header-content h2 {
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-size: 1.8rem;
    font-weight: 600;
}

.stats-summary {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-turquesa);
    font-family: var(--font-secondary);
}

.stat-label {
    font-size: 0.85rem;
    color: var(--color-text-secondary);
    margin-top: 0.25rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--color-surface);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
}

.search-and-sort {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    align-items: end;
}

.search-box {
    flex: 1;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.sort-controls label {
    color: var(--color-text);
    font-weight: 500;
    font-size: 0.9rem;
}

.sort-select {
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    background: var(--color-surface-variant);
    color: var(--color-text);
    font-size: 0.9rem;
    min-width: 150px;
}

.sort-order-btn {
    background: var(--color-surface-variant);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all var(--duration-normal) var(--easing-default);
    font-size: 1rem;
}

.sort-order-btn:hover {
    background: var(--color-turquesa);
    color: white;
    border-color: var(--color-turquesa);
}

.search-box {
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 0.75rem;
    background: var(--color-surface-variant);
    color: var(--color-text);
    font-size: 1rem;
    transition: all var(--duration-normal) var(--easing-default);
}

.search-input:focus {
    outline: none;
    border-color: var(--color-turquesa);
    box-shadow: 0 0 0 3px rgba(0, 204, 204, 0.2);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    background: var(--color-surface-variant);
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--duration-normal) var(--easing-default);
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover {
    border-color: var(--color-turquesa);
    color: var(--color-text);
}

.filter-btn.active {
    background: var(--color-turquesa);
    color: white;
    border-color: var(--color-turquesa);
}

/* Table */
.users-table-container {
    background: var(--color-surface);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--color-border);
    overflow-x: auto;
    overflow-y: visible;
    width: 100%;
    position: relative;
    /* Mejorar scroll horizontal */
    scrollbar-width: thin;
    scrollbar-color: var(--color-turquesa) var(--color-surface-variant);
}

/* Estilos para scrollbar en webkit */
.users-table-container::-webkit-scrollbar {
    height: 8px;
}

.users-table-container::-webkit-scrollbar-track {
    background: var(--color-surface-variant);
    border-radius: 4px;
}

.users-table-container::-webkit-scrollbar-thumb {
    background: var(--color-turquesa);
    border-radius: 4px;
}

.users-table-container::-webkit-scrollbar-thumb:hover {
    background: var(--color-azul);
}

/* Indicador de scroll horizontal mejorado */
.users-table-container::after {
    content: "← Desliza horizontalmente para ver más columnas →";
    position: absolute;
    bottom: 15px;
    right: 20px;
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    opacity: 0.9;
    pointer-events: none;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 204, 204, 0.3);
    animation: pulse 2s infinite;
}

/* Ocultar indicador cuando no hay scroll */
.users-table-container:not([data-scrollable])::after {
    display: none;
}

/* Animación de pulso para el indicador */
@keyframes pulse {
    0%, 100% {
        opacity: 0.9;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1400px; /* Aumentar ancho mínimo para mejor distribución */
    table-layout: auto; /* Permitir ajuste automático de columnas */
}

/* Anchos específicos de columnas optimizados */
.users-table th:nth-child(1), /* Usuario */
.users-table td:nth-child(1) {
    width: 280px;
    min-width: 280px;
}

.users-table th:nth-child(2), /* Comuna */
.users-table td:nth-child(2) {
    width: 160px;
    min-width: 160px;
}

.users-table th:nth-child(3), /* Edad */
.users-table td:nth-child(3) {
    width: 100px;
    min-width: 100px;
    text-align: center;
}

.users-table th:nth-child(4), /* Plan */
.users-table td:nth-child(4) {
    width: 180px;
    min-width: 180px;
}

.users-table th:nth-child(5), /* Tarjetas */
.users-table td:nth-child(5) {
    width: 280px;
    min-width: 280px;
}

.users-table th:nth-child(6), /* Registro */
.users-table td:nth-child(6) {
    width: 200px;
    min-width: 200px;
}

.users-table th:nth-child(7), /* Estado */
.users-table td:nth-child(7) {
    width: 140px;
    min-width: 140px;
    text-align: center;
}

.users-table th:nth-child(8), /* Acciones */
.users-table td:nth-child(8) {
    width: 120px;
    min-width: 120px;
    text-align: center;
}

.users-table th {
    background: var(--color-surface-variant);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--color-text);
    border-bottom: 1px solid var(--color-border);
    font-size: 0.9rem;
}

.users-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--color-border);
    vertical-align: top;
}

.user-row:hover {
    background: var(--color-surface-variant);
}

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar .avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-details strong {
    color: var(--color-text);
    font-size: 1rem;
}

.user-email {
    color: var(--color-text-secondary);
    font-size: 0.85rem;
}

.user-rut {
    color: var(--color-text-secondary);
    font-size: 0.8rem;
    font-family: var(--font-secondary);
}

/* Comuna Info */
.comuna-info {
    min-width: 120px;
}

.location-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.comuna {
    color: var(--color-text);
    font-size: 0.9rem;
    font-weight: 500;
}

.region {
    color: var(--color-text-secondary);
    font-size: 0.75rem;
}

/* Edad Info */
.edad-info {
    min-width: 80px;
    text-align: center;
}

.age-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.age {
    color: var(--color-text);
    font-size: 1.1rem;
    font-weight: 600;
    font-family: var(--font-secondary);
}

.birth-date {
    color: var(--color-text-secondary);
    font-size: 0.75rem;
}

/* Plan Info */
.plan-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.plan-badge.premium {
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    color: white;
}

.plan-badge.gratuito {
    background: rgba(108, 117, 125, 0.2);
    color: var(--color-text-secondary);
}

.plan-details {
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.plan-details small {
    font-size: 0.75rem;
    color: var(--color-text-secondary);
}

.days-remaining {
    font-weight: 600;
}

.days-remaining.normal {
    color: var(--color-success);
}

.days-remaining.warning {
    color: var(--color-warning);
}

.days-remaining.expired {
    color: var(--color-error);
}

/* Cards Info */
.cards-summary {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cards-count {
    font-size: 0.9rem;
    color: var(--color-text);
}

.banks-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.bank-tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: var(--color-surface-variant);
    border-radius: 0.5rem;
    font-size: 0.75rem;
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
    cursor: help;
}

.no-cards-text {
    color: var(--color-text-secondary);
    font-style: italic;
    font-size: 0.9rem;
}

/* Registration Info */
.registration-date {
    font-size: 0.9rem;
    color: var(--color-text);
    margin-bottom: 0.25rem;
}

.source-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.source-badge.admin {
    background: rgba(220, 53, 69, 0.2);
    color: var(--color-error);
}

.source-badge.vendedor {
    background: rgba(0, 204, 204, 0.2);
    color: var(--color-turquesa);
}

.vendor-info {
    margin-top: 0.25rem;
}

.vendor-info small {
    font-size: 0.75rem;
    color: var(--color-text-secondary);
}

/* Role Badges */
.role-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-badge.admin {
    background: rgba(220, 53, 69, 0.2);
    color: var(--color-error);
}

.role-badge.vendedor {
    background: rgba(0, 204, 204, 0.2);
    color: var(--color-turquesa);
}

.role-badge.usuario {
    background: rgba(108, 117, 125, 0.2);
    color: var(--color-text-secondary);
}

.creation-source {
    margin-top: 0.25rem;
}

.creation-source small {
    font-size: 0.75rem;
    color: var(--color-text-secondary);
}

/* Verification Badges */
.verification-info {
    text-align: center;
    min-width: 120px;
}

.verification-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.verification-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.verification-badge.auto-approved {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.verification-badge.pending {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.verification-badge.approved {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.verification-badge.verified {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
}

.verification-badge.rejected {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.verification-badge.unknown {
    background: rgba(108, 117, 125, 0.2);
    color: var(--color-text-secondary);
}

.verified-badge-indicator {
    margin-top: 0.25rem;
}

.badge-verified {
    display: inline-block;
    padding: 0.15rem 0.5rem;
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    border-radius: 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

/* Status Toggle */
.status-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--color-turquesa);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

input:disabled + .toggle-slider {
    opacity: 0.5;
    cursor: not-allowed;
}

.status-label {
    font-size: 0.85rem;
    font-weight: 500;
}

.status-label.activo {
    color: var(--color-success);
}

.status-label.inactivo {
    color: var(--color-error);
}

/* Actions */
.actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    min-width: 100px;
    padding: 0.5rem;
}

.btn-icon {
    background: var(--color-surface-variant);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: all var(--duration-normal) var(--easing-default);
    font-size: 1rem;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: var(--color-surface-variant);
    transform: translateY(-1px);
}

.btn-icon.view:hover {
    border-color: var(--color-turquesa);
    color: var(--color-turquesa);
}

.btn-icon.disable:hover {
    border-color: var(--color-error);
    color: var(--color-error);
}

.btn-icon.enable:hover {
    border-color: var(--color-success);
    color: var(--color-success);
}

.btn-icon.edit:hover {
    border-color: var(--color-azul);
    color: var(--color-azul);
}

/* Responsive Design */
@media (max-width: 1400px) {
    .users-table {
        min-width: 1000px;
    }

    /* Reducir anchos de columnas */
    .users-table th:nth-child(1), .users-table td:nth-child(1) { width: 200px; }
    .users-table th:nth-child(2), .users-table td:nth-child(2) { width: 120px; }
    .users-table th:nth-child(4), .users-table td:nth-child(4) { width: 130px; }
    .users-table th:nth-child(5), .users-table td:nth-child(5) { width: 180px; }
    .users-table th:nth-child(6), .users-table td:nth-child(6) { width: 160px; }
}

@media (max-width: 1200px) {
    .users-table {
        min-width: 900px;
    }

    /* Ocultar información secundaria en pantallas pequeñas */
    .region, .birth-date, .vendor-info, .creation-source {
        display: none;
    }

    /* Reducir padding */
    .users-table th, .users-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Pantallas grandes - Optimizar uso del espacio */
@media (min-width: 1400px) {
    .users-table-container {
        margin: 0 -1rem; /* Expandir hacia los lados */
    }

    .users-table {
        min-width: 100%; /* Usar todo el ancho disponible */
    }

    /* Redistribuir anchos en pantallas grandes */
    .users-table th:nth-child(1),
    .users-table td:nth-child(1) {
        width: 320px;
    }

    .users-table th:nth-child(5),
    .users-table td:nth-child(5) {
        width: 320px;
    }

    .users-table th:nth-child(6),
    .users-table td:nth-child(6) {
        width: 240px;
    }
}

@media (min-width: 1600px) {
    .users-table-container {
        margin: 0 -2rem; /* Más expansión en pantallas muy grandes */
    }

    .users-table th:nth-child(1),
    .users-table td:nth-child(1) {
        width: 360px;
    }

    .users-table th:nth-child(5),
    .users-table td:nth-child(5) {
        width: 360px;
    }
}

@media (max-width: 768px) {
    .users-table {
        min-width: 800px;
    }

    /* Simplificar vista móvil */
    .user-rut {
        display: none;
    }

    .plan-details {
        display: none;
    }

    .banks-list {
        display: none;
    }
}

/* Loading and Empty States */
.loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-surface-variant);
    border-top: 4px solid var(--color-turquesa);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--color-text);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--color-text-secondary);
    margin: 0;
}

/* Responsive */
@media (max-width: 1200px) {
    .stats-summary {
        gap: 1rem;
    }
    
    .banks-list {
        max-width: 200px;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn var(--duration-normal) var(--easing-default);
    padding: 1rem;
}

.modal-content.extra-large {
    background: var(--color-surface);
    border-radius: 1rem;
    max-width: 1200px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: scaleIn var(--duration-normal) var(--easing-default);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface-variant);
    border-radius: 1rem 1rem 0 0;
}

.user-header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-header-info .user-avatar .avatar-placeholder {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
}

.user-title h3 {
    margin: 0 0 0.5rem 0;
    color: var(--color-text);
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: color var(--duration-normal) var(--easing-default);
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.close-btn:hover {
    color: var(--color-text);
    background: var(--color-surface);
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface-variant);
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--duration-normal) var(--easing-default);
    font-weight: 500;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    color: var(--color-text);
    background: var(--color-surface);
}

.tab-btn.active {
    color: var(--color-turquesa);
    border-bottom-color: var(--color-turquesa);
    background: var(--color-surface);
}

.tab-content {
    padding: 2rem;
}

.tab-content h4 {
    margin: 0 0 1.5rem 0;
    color: var(--color-text);
    font-size: 1.3rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--color-border);
}

/* Editable Fields */
.editable-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.field-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.field-group label {
    color: var(--color-text);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Plan Tab */
.plan-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.current-plan {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--color-surface-variant);
    border-radius: 1rem;
    border: 1px solid var(--color-border);
}

.plan-badge-large {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.plan-badge-large.premium {
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    color: white;
}

.plan-badge-large.gratuito {
    background: rgba(108, 117, 125, 0.2);
    color: var(--color-text-secondary);
}

.plan-details p {
    margin: 0.5rem 0;
    color: var(--color-text-secondary);
}

.plan-actions {
    display: flex;
    gap: 1rem;
}

.premium-details {
    background: linear-gradient(135deg, rgba(0, 204, 204, 0.1), rgba(28, 148, 224, 0.1));
    border: 2px solid var(--color-turquesa);
    border-radius: 1rem;
    padding: 1.5rem;
}

.premium-details h5 {
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-size: 1.1rem;
    font-weight: 600;
}

.premium-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
    border: 1px solid var(--color-border);
}

.info-item label {
    color: var(--color-text-secondary);
    font-weight: 500;
}

.premium-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Cards Tab */
.cards-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.card-item {
    background: var(--color-surface-variant);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--color-border);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-title h5 {
    margin: 0;
    color: var(--color-text);
    font-size: 1.1rem;
    font-weight: 600;
}

.tipo-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.tipo-badge.bank {
    background: rgba(28, 148, 224, 0.2);
    color: var(--color-azul);
}

.tipo-badge.wallet {
    background: rgba(0, 204, 204, 0.2);
    color: var(--color-turquesa);
}

.tipo-badge.prepaid_card {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.status-badge.active {
    background: rgba(40, 167, 69, 0.2);
    color: var(--color-success);
}

.status-badge.inactive {
    background: rgba(220, 53, 69, 0.2);
    color: var(--color-error);
}

.card-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
}

.detail-row .label {
    color: var(--color-text-secondary);
    font-weight: 500;
}

.detail-row .value {
    color: var(--color-text);
    font-family: var(--font-secondary);
}

.card-stats {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
}

.stat-icon {
    font-size: 1rem;
}

.stat-value {
    color: var(--color-text-secondary);
    font-size: 0.85rem;
}

/* System Tab */
.system-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.info-section h5 {
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-size: 1.1rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--color-border);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.monospace {
    font-family: var(--font-secondary);
    background: var(--color-surface-variant);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.85rem;
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--color-border);
    background: var(--color-surface-variant);
    border-radius: 0 0 1rem 1rem;
}

.last-updated {
    color: var(--color-text-secondary);
    font-size: 0.85rem;
}

.footer-actions {
    display: flex;
    gap: 1rem;
}

/* No Cards State */
.no-cards {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: var(--color-text-secondary);
}

.no-cards .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .management-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-summary {
        justify-content: center;
    }

    .users-table-container {
        overflow-x: auto;
    }

    .users-table {
        min-width: 800px;
    }

    .filter-buttons {
        justify-content: center;
    }

    .modal-content.extra-large {
        margin: 0.5rem;
        width: calc(100% - 1rem);
        max-height: 95vh;
    }

    .tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }

    .editable-fields {
        grid-template-columns: 1fr;
    }

    .current-plan {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .plan-actions {
        justify-content: center;
    }

    .premium-info {
        grid-template-columns: 1fr;
    }

    .card-details {
        grid-template-columns: 1fr;
    }

    .card-stats {
        flex-direction: column;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}
