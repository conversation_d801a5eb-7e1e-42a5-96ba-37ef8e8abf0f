<template>
    <div class="home">
        <NavbarHome />
        <HeroSectionHome />
        <TransferCounter />
        <BenefitsSection />
        <FooterHome />
        <WhatsAppButton />
    </div>
</template>

<script setup>
import NavbarHome from '@/components/home/<USER>'
import HeroSectionHome from '@/components/home/<USER>'
import TransferCounter from '@/components/home/<USER>'
import BenefitsSection from '@/components/home/<USER>'
import FooterHome from '@/components/home/<USER>'
import WhatsAppButton from '@/components/home/<USER>'
</script>

<style scoped>
.home {
    background: #121212;
    color: #fff;
    font-family: 'Montserrat', 'Roboto', 'Raleway', sans-serif;
    margin: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Asegurar que el layout sea fluido */
.home * {
    box-sizing: border-box;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Links globales */
.home a {
    color: #00cccc;
    text-decoration: none;
    transition: color 0.2s;
}

.home a:hover {
    color: #1c94e0;
}
</style>
