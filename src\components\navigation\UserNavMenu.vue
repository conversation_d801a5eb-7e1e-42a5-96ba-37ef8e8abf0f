<template>
  <div class="user-nav-menu">
    <!-- Avatar y dropdown cuando hay sesión -->
    <div v-if="isAuthenticated" class="user-dropdown">
      <v-menu offset-y>
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            class="user-avatar-btn"
            variant="text"
            size="large"
          >
            <v-avatar size="40" class="user-avatar">
              <img 
                v-if="userPhoto" 
                :src="userPhoto" 
                :alt="userName"
              />
              <span v-else class="avatar-initials">
                {{ userInitials }}
              </span>
            </v-avatar>
            <v-icon class="dropdown-icon">mdi-chevron-down</v-icon>
          </v-btn>
        </template>

        <v-list class="user-menu-list">
          <!-- Información del usuario -->
          <v-list-item class="user-info-item">
            <v-list-item-title class="user-name">{{ userName }}</v-list-item-title>
            <v-list-item-subtitle class="user-email">{{ userEmail }}</v-list-item-subtitle>
            <v-chip 
              :color="userTypeColor" 
              size="small" 
              variant="elevated"
              class="user-type-chip"
            >
              {{ userTypeLabel }}
            </v-chip>
          </v-list-item>

          <v-divider />

          <!-- Opciones según el rol -->
          <v-list-item 
            v-for="option in menuOptions" 
            :key="option.key"
            @click="handleMenuClick(option.action)"
            class="menu-option"
          >
            <template v-slot:prepend>
              <v-icon :color="option.color">{{ option.icon }}</v-icon>
            </template>
            <v-list-item-title>{{ option.label }}</v-list-item-title>
          </v-list-item>

          <v-divider />

          <!-- Cerrar sesión -->
          <v-list-item @click="logout" class="logout-option">
            <template v-slot:prepend>
              <v-icon color="error">mdi-logout</v-icon>
            </template>
            <v-list-item-title>Cerrar Sesión</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </div>

    <!-- Botones de login/registro cuando no hay sesión -->
    <div v-else class="auth-buttons">
      <v-btn
        @click="goToLogin"
        variant="outlined"
        color="turquesa"
        class="login-btn"
      >
        Iniciar Sesión
      </v-btn>
      <v-btn
        @click="goToRegister"
        color="turquesa"
        variant="elevated"
        class="register-btn"
      >
        Registrarse
      </v-btn>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'

// Router y stores
const router = useRouter()
const authStore = useAuthStore()

// Computed properties
const isAuthenticated = computed(() => authStore.isAuthenticated)
const userEmail = computed(() => authStore.user?.email || '')
const userName = computed(() => {
  if (authStore.userInfo?.nombre && authStore.userInfo?.apellido) {
    return `${authStore.userInfo.nombre} ${authStore.userInfo.apellido}`
  }
  return authStore.userInfo?.nombre || authStore.user?.displayName || 'Usuario'
})

const userPhoto = computed(() => {
  return authStore.user?.photoURL || authStore.userInfo?.photoURL || null
})

const userInitials = computed(() => {
  const name = userName.value
  const parts = name.split(' ')
  if (parts.length >= 2) {
    return `${parts[0][0]}${parts[1][0]}`.toUpperCase()
  }
  return name.substring(0, 2).toUpperCase()
})

const userRole = computed(() => authStore.userInfo?.rol || 'usuario')
const isPremium = computed(() => authStore.userInfo?.isPremium || false)

const userTypeLabel = computed(() => {
  if (userRole.value === 'admin') return 'Administrador'
  if (userRole.value === 'vendedor') return 'Vendedor'
  if (userRole.value === 'soporte') return 'Soporte'
  return isPremium.value ? 'Premium' : 'Gratuito'
})

const userTypeColor = computed(() => {
  if (userRole.value === 'admin') return 'error'
  if (userRole.value === 'vendedor') return 'warning'
  if (userRole.value === 'soporte') return 'info'
  return isPremium.value ? 'success' : 'grey'
})

const menuOptions = computed(() => {
  const options = []
  
  // Opciones comunes para todos los usuarios
  options.push({
    key: 'dashboard',
    label: 'Mi Dashboard',
    icon: 'mdi-view-dashboard',
    color: 'primary',
    action: 'dashboard'
  })
  
  options.push({
    key: 'profile',
    label: 'Mi Perfil',
    icon: 'mdi-account-edit',
    color: 'info',
    action: 'profile'
  })
  
  // Opciones específicas por rol
  if (userRole.value === 'admin') {
    options.push({
      key: 'admin',
      label: 'Panel de Administración',
      icon: 'mdi-shield-crown',
      color: 'error',
      action: 'admin'
    })
  }
  
  if (userRole.value === 'vendedor') {
    options.push({
      key: 'vendor',
      label: 'Panel de Vendedor',
      icon: 'mdi-store',
      color: 'warning',
      action: 'vendor'
    })
  }
  
  if (userRole.value === 'soporte') {
    options.push({
      key: 'support',
      label: 'Panel de Soporte',
      icon: 'mdi-headset',
      color: 'info',
      action: 'support'
    })
  }
  
  // Opción de upgrade para usuarios gratuitos
  if (!isPremium.value && userRole.value === 'usuario') {
    options.push({
      key: 'upgrade',
      label: 'Actualizar a Premium',
      icon: 'mdi-star',
      color: 'success',
      action: 'upgrade'
    })
  }
  
  return options
})

// Methods
const handleMenuClick = (action) => {
  switch (action) {
    case 'dashboard':
      if (userRole.value === 'admin') {
        router.push('/admin')
      } else if (userRole.value === 'vendedor') {
        router.push('/vendedor')
      } else if (userRole.value === 'soporte') {
        router.push('/soporte')
      } else {
        router.push('/usuario')
      }
      break
      
    case 'profile':
      router.push('/perfil')
      break
      
    case 'admin':
      router.push('/admin')
      break
      
    case 'vendor':
      router.push('/vendedor')
      break
      
    case 'support':
      router.push('/soporte')
      break
      
    case 'upgrade':
      router.push('/planes')
      break
      
    default:
      console.log('Acción no reconocida:', action)
  }
}

const goToLogin = () => {
  router.push('/login')
}

const goToRegister = () => {
  router.push('/registro')
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Error al cerrar sesión:', error)
  }
}
</script>

<style scoped>
.user-nav-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-dropdown {
  position: relative;
}

.user-avatar-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.user-avatar-btn:hover {
  background: rgba(0, 204, 204, 0.1);
}

.user-avatar {
  border: 2px solid var(--color-turquesa);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.avatar-initials {
  background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.dropdown-icon {
  color: var(--color-turquesa);
  transition: transform 0.3s ease;
}

.user-avatar-btn:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user-menu-list {
  min-width: 280px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.user-info-item {
  padding: 1rem;
  background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
  color: white;
  border-radius: 12px 12px 0 0;
}

.user-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: white !important;
}

.user-email {
  opacity: 0.9;
  font-size: 0.9rem;
  color: white !important;
}

.user-type-chip {
  margin-top: 0.5rem;
}

.menu-option {
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.menu-option:hover {
  background: rgba(0, 204, 204, 0.1);
}

.logout-option {
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.logout-option:hover {
  background: rgba(244, 67, 54, 0.1);
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.login-btn {
  border-color: var(--color-turquesa);
  color: var(--color-turquesa);
}

.register-btn {
  background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
  color: white;
}

/* Responsive */
@media (max-width: 768px) {
  .auth-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .auth-buttons .v-btn {
    width: 100%;
    min-width: 120px;
  }
  
  .user-menu-list {
    min-width: 250px;
  }
}
</style>
