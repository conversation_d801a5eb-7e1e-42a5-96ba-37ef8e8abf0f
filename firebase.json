{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}