/* User Dashboard Styles */
.user-dashboard {
    min-height: 100vh;
    background: var(--color-background);
    padding: 2rem;
    font-family: var(--font-primary);
    animation: fadeIn var(--duration-normal) var(--easing-default);
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--color-surface);
    border-radius: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--color-border);
    animation: slideUp var(--duration-normal) var(--easing-default);
}

.user-welcome {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.8rem;
    text-transform: uppercase;
}

.welcome-content h1 {
    margin: 0 0 0.5rem 0;
    color: var(--color-text);
    font-size: 2.5rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-plan {
    margin: 0 0 0.25rem 0;
    color: var(--color-text-secondary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.premium-badge {
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.premium-expiry {
    margin: 0;
    color: var(--color-warning);
    font-size: 0.95rem;
    font-weight: 500;
}

.premium-expired {
    margin: 0;
    color: var(--color-error);
    font-size: 0.95rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Notificaciones */
.notifications-section {
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 1rem;
    border: 1px solid;
    animation: slideDown var(--duration-normal) var(--easing-default);
}

.notification.warning {
    background: rgba(255, 193, 7, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning);
}

.notification.error {
    background: rgba(220, 53, 69, 0.1);
    border-color: var(--color-error);
    color: var(--color-error);
}

.notification.info {
    background: rgba(13, 202, 240, 0.1);
    border-color: var(--color-info);
    color: var(--color-info);
}

.notification-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.notification-content p {
    margin: 0;
    font-size: 0.95rem;
    opacity: 0.9;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity var(--duration-normal) var(--easing-default);
}

.notification-close:hover {
    opacity: 1;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--color-surface);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
    transition: all var(--duration-normal) var(--easing-default);
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideUp var(--duration-normal) var(--easing-default);
    animation-delay: 0.1s;
    animation-fill-mode: both;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-turquesa);
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-content h3 {
    margin: 0 0 0.5rem 0;
    color: var(--color-text);
    font-size: 1rem;
    font-weight: 500;
}

.stat-number {
    margin: 0 0 0.25rem 0;
    color: var(--color-text);
    font-size: 2rem;
    font-weight: 700;
}

.stat-limit, .stat-label {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

/* Tabs */
.tabs-container {
    margin-bottom: 2rem;
}

.tabs {
    display: flex;
    gap: 0.5rem;
    background: var(--color-surface-variant);
    padding: 0.5rem;
    border-radius: 1rem;
    border: 1px solid var(--color-border);
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    border-radius: 0.75rem;
    color: var(--color-text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--duration-normal) var(--easing-default);
    font-size: 0.95rem;
}

.tab-btn:hover {
    color: var(--color-text);
    background: var(--color-surface);
}

.tab-btn.active {
    background: var(--color-surface);
    color: var(--color-text);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border);
}

/* Tab Content */
.tab-content {
    background: var(--color-surface);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
    min-height: 400px;
}

/* Loading & Empty States */
.loading-state, .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--color-text-secondary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-surface-variant);
    border-top: 4px solid var(--color-turquesa);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0 0 2rem 0;
    font-size: 1.1rem;
    line-height: 1.5;
}

/* Tarjetas Grid */
.tarjetas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.tarjeta-card {
    background: var(--color-surface-variant);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--color-border);
    transition: all var(--duration-normal) var(--easing-default);
}

.tarjeta-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-turquesa);
}

.tarjeta-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

.tarjeta-header h4 {
    margin: 0;
    color: var(--color-text);
    font-size: 1.2rem;
    font-weight: 600;
}

.tarjeta-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: var(--color-surface);
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--duration-normal) var(--easing-default);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.btn-icon:hover {
    background: var(--color-turquesa);
    color: white;
    transform: scale(1.1);
}

.btn-icon.edit:hover { background: var(--color-warning); }
.btn-icon.delete:hover { background: var(--color-error); }

.tarjeta-info {
    margin-bottom: 1rem;
}

.tarjeta-info p {
    margin: 0.5rem 0;
    color: var(--color-text-secondary);
    font-size: 0.95rem;
}

.tarjeta-info strong {
    color: var(--color-text);
}

.banco-tipo {
    margin: 0.25rem 0 !important;
}

.tipo-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tipo-badge.bank {
    background: rgba(28, 148, 224, 0.2);
    color: var(--color-azul);
}

.tipo-badge.wallet {
    background: rgba(0, 204, 204, 0.2);
    color: var(--color-turquesa);
}

.tipo-badge.prepaid_card {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.tarjeta-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
}

.tarjeta-stats .stat {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
}

.tarjeta-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tarjeta-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background: var(--color-success);
    color: white;
}

.status-badge.inactive {
    background: var(--color-surface);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
}

/* Estadísticas Section */
.estadisticas-section {
    animation: fadeIn var(--duration-normal) var(--easing-default);
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

.stats-header h3 {
    margin: 0;
    color: var(--color-text);
    font-size: 1.5rem;
    font-weight: 600;
}

.period-selector select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    background: var(--color-surface);
    color: var(--color-text);
    font-size: 0.95rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.stats-card {
    background: var(--color-surface-variant);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--color-border);
    transition: all var(--duration-normal) var(--easing-default);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stats-card h4 {
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Charts */
.chart-placeholder {
    text-align: center;
    padding: 1rem;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.real-chart {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    text-align: left;
}

.chart-container {
    display: flex;
    align-items: end;
    height: 120px;
    gap: 8px;
    margin-bottom: 10px;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, #00cccc, #1c94e0);
    border-radius: 4px 4px 0 0;
    min-height: 5px;
    position: relative;
    display: flex;
    align-items: end;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-bar:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

.bar-value {
    position: absolute;
    top: -20px;
    font-size: 0.7rem;
    font-weight: 600;
    color: #333;
}

.chart-labels {
    display: flex;
    justify-content: space-around;
    font-size: 0.7rem;
    color: #666;
}

.day-label {
    text-align: center;
    flex: 1;
}

.mock-chart {
    display: flex;
    align-items: end;
    justify-content: space-around;
    height: 100px;
    margin-top: 1rem;
    gap: 0.5rem;
}

.chart-bar {
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    border-radius: 0.25rem;
    min-height: 20px;
    flex: 1;
    transition: all var(--duration-normal) var(--easing-default);
}

.chart-bar:hover {
    opacity: 0.8;
    transform: scaleY(1.1);
}

/* Bank Stats */
.bank-stats, .device-stats, .time-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.bank-item, .device-item, .time-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
}

.bank-item span:first-child,
.device-item span:nth-child(2),
.time-item span:first-child {
    flex: 1;
    color: var(--color-text);
    font-weight: 500;
}

.progress-bar {
    flex: 2;
    height: 8px;
    background: var(--color-surface-variant);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    border-radius: 4px;
    transition: width var(--duration-slow) var(--easing-default);
}

.device-icon {
    font-size: 1.2rem;
}

.device-percentage, .time-percentage {
    color: var(--color-text);
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

/* Configuración Section */
.configuracion-section {
    animation: fadeIn var(--duration-normal) var(--easing-default);
}

.config-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

.config-header h3 {
    margin: 0;
    color: var(--color-text);
    font-size: 1.5rem;
    font-weight: 600;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.config-card {
    background: var(--color-surface-variant);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid var(--color-border);
    transition: all var(--duration-normal) var(--easing-default);
}

.config-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.config-card h4 {
    margin: 0 0 1rem 0;
    color: var(--color-text);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.config-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.config-content p {
    margin: 0;
    color: var(--color-text-secondary);
    font-size: 0.95rem;
}

.config-content strong {
    color: var(--color-text);
}

/* Plan Card */
.plan-card {
    background: linear-gradient(135deg, rgba(0, 204, 204, 0.1), rgba(28, 148, 224, 0.1));
    border: 2px solid var(--color-turquesa);
}

.plan-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.plan-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-text);
    text-transform: capitalize;
}

.plan-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.plan-badge.premium {
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    color: white;
}

.plan-badge.free {
    background: var(--color-surface);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
}

.plan-details, .upgrade-section {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 0.75rem;
    border: 1px solid var(--color-border);
}

.upgrade-section p {
    margin-bottom: 1rem;
    text-align: center;
}

/* Notification Settings */
.notification-setting {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--color-surface);
    border-radius: 0.5rem;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-surface-variant);
    transition: var(--duration-normal);
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--duration-normal);
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--color-turquesa);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn var(--duration-normal) var(--easing-default);
}

.modal-content {
    background: var(--color-surface);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: scaleIn var(--duration-normal) var(--easing-default);
}

.modal-content.large {
    max-width: 800px;
    width: 95%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
    margin: 0;
    color: var(--color-text);
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: color var(--duration-normal) var(--easing-default);
}

.close-btn:hover {
    color: var(--color-text);
}

.card-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border);
}

/* Button Sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-ghost {
    background: none;
    border: 1px solid var(--color-border);
    color: var(--color-text-secondary);
}

.btn-ghost:hover {
    background: var(--color-surface-variant);
    color: var(--color-text);
}

.btn-turquesa {
    background: linear-gradient(135deg, var(--color-turquesa), var(--color-azul));
    color: white;
    border: none;
}

.btn-turquesa:hover {
    background: linear-gradient(135deg, var(--color-azul), var(--color-turquesa));
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .user-dashboard {
        padding: 1rem;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .user-welcome {
        flex-direction: column;
        text-align: center;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .quick-stats {
        grid-template-columns: 1fr;
    }

    .tabs {
        flex-direction: column;
    }

    .tab-content {
        padding: 1rem;
    }

    .tarjetas-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }

    .tarjeta-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .tarjeta-footer {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .tarjeta-actions {
        justify-content: center;
    }

    .stats-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content.large {
        max-width: 95%;
        padding: 1.5rem;
    }

    .notification {
        flex-direction: column;
        text-align: center;
    }

    .plan-info {
        flex-direction: column;
        text-align: center;
    }
}

/* Animaciones adicionales */
.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.tarjeta-card:nth-child(odd) { animation-delay: 0.1s; }
.tarjeta-card:nth-child(even) { animation-delay: 0.2s; }

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

.config-card:nth-child(1) { animation-delay: 0.1s; }
.config-card:nth-child(2) { animation-delay: 0.2s; }
.config-card:nth-child(3) { animation-delay: 0.3s; }
.config-card:nth-child(4) { animation-delay: 0.4s; }

/* Modal Styles para Editar Perfil y Cambiar Contraseña */
.edit-profile-form,
.change-password-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--color-text);
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 0.75rem;
    background: var(--color-surface-variant);
    color: var(--color-text);
    font-size: 1rem;
    transition: all var(--duration-normal) var(--easing-default);
}

.form-input:focus {
    outline: none;
    border-color: var(--color-turquesa);
    box-shadow: 0 0 0 3px rgba(0, 204, 204, 0.2);
    background-color: var(--color-surface);
}

.form-input::placeholder {
    color: var(--color-text-secondary);
    opacity: 0.7;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border);
}

/* Estilos específicos para GeoSelector en modal */
.edit-profile-form .geo-selector {
    margin-top: 0.5rem;
}

.edit-profile-form .form-label {
    font-weight: 600;
    color: var(--color-text);
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Responsive para modales */
@media (max-width: 768px) {
    .modal-actions {
        flex-direction: column;
    }

    .form-input {
        font-size: 16px; /* Evita zoom en iOS */
    }
}

/* Estilos específicos para gráficos reales */
.bank-name {
    min-width: 120px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--color-text);
}

.bank-count {
    min-width: 60px;
    font-size: 0.8rem;
    color: #666;
    text-align: right;
}

.device-name {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--color-text);
}

.no-data {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Indicador de carga para filtros */
.loading-indicator {
    display: flex;
    align-items: center;
    margin-left: 0.5rem;
}

.spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #00cccc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.period-selector {
    display: flex;
    align-items: center;
}

.period-selector select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* ✅ NUEVO: Estilos para UserProfileCard y DynamicBanners */
.profile-section {
    margin-bottom: 2rem;
}

.dashboard-header {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Responsive para nuevos componentes */
@media (max-width: 768px) {
    .profile-section {
        margin-bottom: 1rem;
    }

    .dashboard-header {
        gap: 1rem;
    }
}
