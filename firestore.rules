rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Función para verificar si el usuario está autenticado
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Función para verificar si el usuario es admin
    function isAdmin() {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.rol == 'admin';
    }

    // Función para verificar si el usuario es vendedor
    function isVendedor() {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.rol == 'vendedor';
    }
    
    // Función para verificar si es el propio usuario
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Reglas para la colección de usuarios
    match /users/{userId} {
      // Los admins pueden leer y escribir todos los usuarios
      allow read, write: if isAdmin();
      
      // Los vendedores pueden leer usuarios
      allow read: if isVendedor();
      
      // Los usuarios pueden leer y actualizar su propia información
      allow read, update: if isOwner(userId);
      
      // Permitir creación de usuarios (para registro)
      allow create: if isAuthenticated();
    }
    
    // Reglas para cuentas bancarias
    match /bank_accounts/{accountId} {
      // Los admins tienen acceso completo
      allow read, write: if isAdmin();
      
      // Los vendedores pueden leer
      allow read: if isVendedor();
      
      // Los usuarios pueden leer sus propias cuentas
      allow read: if isAuthenticated() && 
                     resource.data.rutTitular == get(/databases/$(database)/documents/users/$(request.auth.uid)).data.rut;
    }
    
    // Reglas para transacciones
    match /transactions/{transactionId} {
      // Los admins tienen acceso completo
      allow read, write: if isAdmin();
      
      // Los vendedores pueden leer
      allow read: if isVendedor();
      
      // Los usuarios pueden leer sus propias transacciones
      allow read: if isAuthenticated();
      
      // Permitir crear transacciones para usuarios autenticados
      allow create: if isAuthenticated();
    }
    
    // Reglas para configuraciones del sistema
    match /settings/{settingId} {
      // Solo los admins pueden leer y escribir configuraciones
      allow read, write: if isAdmin();
    }

    // Reglas para métricas globales
    match /metrics/{document} {
      // Todos los usuarios autenticados pueden leer métricas globales
      allow read: if isAuthenticated();
      // Solo los admins pueden escribir métricas globales
      allow write: if isAdmin();
    }

    // Reglas para métricas por usuario
    match /user_metrics/{userId} {
      // Los usuarios pueden leer y escribir sus propias métricas
      allow read, write: if isOwner(userId) || isAdmin();
    }

    // Reglas para visitas detalladas
    match /detailed_visits/{visitId} {
      // Solo admins pueden leer todas las visitas
      // Los usuarios pueden escribir sus propias visitas
      allow read: if isAdmin();
      allow write: if isAuthenticated();
    }

    // Reglas para uso de bancos
    match /bank_usage/{usageId} {
      // Solo admins pueden leer todos los usos
      // Los usuarios pueden escribir sus propios usos
      allow read: if isAdmin();
      allow write: if isAuthenticated();
    }
    
    // Reglas para logs de auditoría
    match /audit_logs/{logId} {
      // Solo los admins pueden leer logs
      allow read: if isAdmin();

      // Permitir crear logs (para el sistema)
      allow create: if isAuthenticated();
    }

    // Reglas para tickets de soporte
    match /tickets/{ticketId} {
      // Los usuarios pueden crear tickets y leer sus propios tickets
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid;

      allow read: if isAuthenticated() &&
        (resource.data.userId == request.auth.uid || isAdmin());

      // Solo los admins pueden actualizar tickets
      allow update: if isAdmin();

      // Solo los admins pueden eliminar tickets
      allow delete: if isAdmin();
    }

    // Reglas para notificaciones
    match /notifications/{notificationId} {
      // Los usuarios pueden leer y actualizar sus propias notificaciones
      allow read, update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;

      // Solo el sistema puede crear notificaciones (a través de Cloud Functions)
      allow create: if isAuthenticated();

      // Los admins pueden leer todas las notificaciones
      allow read: if isAdmin();
    }

    // Reglas para tarjetas bancarias (bank_cards)
    match /bank_cards/{cardId} {
      // Los admins tienen acceso completo
      allow read, write: if isAdmin();

      // Los usuarios pueden gestionar sus propias tarjetas
      allow read, write: if isAuthenticated() &&
        resource.data.propietarioUID == request.auth.uid;

      // Permitir crear tarjetas para usuarios autenticados
      allow create: if isAuthenticated() &&
        request.resource.data.propietarioUID == request.auth.uid;
    }

    // Regla temporal para desarrollo (REMOVER EN PRODUCCIÓN)
    // Permite acceso completo para testing
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
